{"test/components/test_merge_grounding_with_polymer_response.py::test_merge_grounding_basics[response_df0-expected_results0-None]": true, "test/components/test_merge_grounding_with_polymer_response.py::test_missing_files_statistics": true, "tests/components/test_align_batched_output_index.py::test_align_batched_output_index[usr_meeting_minutes_data_generation/meeting_minutes_item/gen_prompt_output-usr_meeting_minutes_data_generation/meeting_minutes_item/llm_api_batched_output-usr_meeting_minutes_data_generation/meeting_minutes_item/llm_api_batched_align_output]": true, "tests/components/test_align_batched_output_index.py::test_align_batched_output_index[usr_meeting_minutes_data_generation/meeting_minutes_item/gen_prompt_output-usr_meeting_minutes_data_generation/meeting_minutes_item/llm_api_batched_parsed_output-usr_meeting_minutes_data_generation/meeting_minutes_item/llm_api_batched_align_output]": true, "tests/components/test_align_batched_output_index.py::test_align_batched_output_index[usr_meeting_minutes_data_generation/meeting_minutes_item/generated_prompt_output-usr_meeting_minutes_data_generation/meeting_minutes_item/llm_api_batched_output-usr_meeting_minutes_data_generation/meeting_minutes_item/llm_api_batched_align_output]": true, "tests/components/test_concat_transcript_snippet.py::test_concat_transcript_snippet[test_concat_transcript_snippet/case1/transcripts_sew/merge_inputs_async_sync_output-test_concat_transcript_snippet/case1/united_transcripts-ordered_keys_list0-True-False]": true, "tests/components/test_concat_transcript_snippet.py::test_concat_transcript_snippet[test_concat_transcript_snippet/transcripts_gen/transcripts_sew/merge_inputs_async_sync_output-test_concat_transcript_snippet/transcripts_gen/transcripts_sew/united_transcripts-ordered_keys_list0-True]": true, "tests/components/test_concat_transcript_snippet.py::test_concat_transcript_snippet[test_concat_transcript_snippet/transcripts_sew1/merge_inputs_async_sync_output-test_concat_transcript_snippet/united_transcripts-ordered_keys_list0-True]": true, "tests/components/test_concat_transcript_snippet.py::test_fix_rewritten_transcript_indexes[case1_no_change/input-case1_no_change/expect]": true, "tests/components/test_concat_transcript_snippet.py::test_fix_rewritten_transcript_indexes[case1_no_change/input-case1_no_change/output]": true, "tests/components/test_concat_transcript_snippet.py::test_fix_rewritten_transcript_indexes[case1_nochange/input-case1_nochange/output]": true, "tests/components/test_concat_transcript_snippet.py::test_fix_rewritten_transcript_indexes[case2_change/input-case2_change/output]": true, "tests/components/test_concat_transcript_snippet.py::test_fix_rewritten_transcript_indexes[case2_remove_transcript/input-case2_remove_transcript/output]": true, "tests/components/test_concat_transcript_snippet.py::test_resolve_overlap_between_sequential_responses[case1/input/-case1/expected/]": true, "tests/components/test_concat_transcript_snippet.py::test_resolve_overlap_between_sequential_responses[case1/input/rewritten_transcript.json-case1/expected/]": true, "tests/components/test_create_inference_data.py::test_create_inference_data[case3/input-case3/output-Missing transcript for item1]": true, "tests/components/test_create_inference_data.py::test_create_inference_data[case4/input-case4/output-Missing completion for item1]": true, "tests/components/test_filter_data.py::test_filter_data[test_data/test_filter_data/-3000-test_data/test_filter_data/]": true, "tests/components/test_filter_data.py::test_filter_data[test_data/test_filter_data/-test_data/test_filter_data-3000]": true, "tests/components/test_filter_data.py::test_filter_data[test_data/test_filter_data/input/-10-test_data/test_filter_data/output/]": true, "tests/components/test_filter_data.py::test_filter_data[test_data/test_filter_data/input/-100-test_data/test_filter_data/output/]": true, "tests/components/test_filter_data.py::test_filter_data[test_data/test_filter_data/input/-2-test_data/test_filter_data/output/]": true, "tests/components/test_filter_data.py::test_filter_data[test_data/test_filter_data/input/-3000-test_data/test_filter_data/output/]": true, "tests/components/test_filter_data.py::test_filter_data[test_data/test_filter_data/input/-50-test_data/test_filter_data/output/]": true, "tests/components/test_filter_user_intent.py::test_filter_user_intent[all_intents_case-10-42-info_messages0-error_messages0]": true, "tests/components/test_generate_grounding_data.py::test_generate_groundingdata[case_normal]": true, "tests/components/test_generate_question_dataset.py::test_generate_same_fixed_question_dataset[case_input_meeting_Participants_empty/input_path-KeyTakeawaysWithSpeakers-case_input_meeting_Participants_empty/output_path-DA692406A54E947_65EC65FF714B554F_1_1 Meeting params.Participants is empty]": true, "tests/components/test_generate_question_dataset.py::test_user_name_extraction_logic[lead_usr_dict2-meeting_0-participants2--meeting_0: No participants found in meeting_params]": true, "tests/components/test_generate_question_dataset.py::test_user_name_extraction_logic[lead_usr_dict4-meeting_0-participants4--None-meeting_0: Using lead user name: ]": true, "tests/components/test_generate_selected_prompt.py::test_generate_selected_prompt[case1/input_data-case1/failed_items-None-]": true, "tests/components/test_generate_selected_prompt.py::test_generate_selected_prompt[input_data-failed_items-output_data-case3-]": true, "tests/components/test_generate_usr_data_seed.py::test_create_seed_from_usr_data[parsed_usr_data-False-False-42-usr_data_seed_output]": true, "tests/components/test_generate_usr_data_seed.py::test_create_seed_from_usr_data[parsed_usr_data-False-True-42-usr_data_seed_output]": true, "tests/components/test_get_value_from_dict.py::test_get_value_from_dict_handles_cycles": true, "tests/components/test_get_value_from_dict.py::test_get_value_from_dict_invalid_inputs[input_dict3-None]": true, "tests/components/test_get_value_from_dict.py::test_get_value_from_dict_preserves_types": true, "tests/components/test_get_value_from_dict.py::test_get_value_from_dict_with_list_cycles": true, "tests/components/test_log_collector.py::test_merge_and_sort_logs[input_data0-expected_result0]": true, "tests/components/test_log_collector.py::test_merge_and_sort_logs[input_data1-expected_result1]": true, "tests/components/test_log_collector.py::test_merge_and_sort_logs[input_data2-expected_result2]": true, "tests/components/test_log_collector.py::test_merge_and_sort_logs[input_logs_dir0]": true, "tests/components/test_log_collector.py::test_parse_log_entry[[component] 04/22/2025 10:15:31 - root - INFO - \\u6d4b\\u8bd5\\u6d88\\u606f with !@#$%-mcp.component-456def.log-expected_result1]": true, "tests/components/test_log_collector.py::test_write_merged_log[sorted_entries0-[2025-04-22 10:15:30,000] [test] 04/22/2025 10:15:30 - root - INFO - First entry\\n[2025-04-22 10:15:31,000] [test] 04/22/2025 10:15:31 - root - INFO - Second entry\\n]": true, "tests/components/test_log_collector.py::test_write_merged_log[sorted_entries1-[2025-04-22 10:15:30,123] [test] 04/22/2025 10:15:30 - root - INFO - With milliseconds\\n]": true, "tests/components/test_merge_consecutive_responses.py::test_merge_consecutive_responses[trainscripts_gen/llm_api_async_sync_parse_output-trainscripts_gen/merge_consecutive_responses]": true, "tests/components/test_merge_consecutive_responses.py::test_merge_consecutive_responses[transcripts_gen/llm_api_async_sync_parse_output-transcripts_gen/merge_consecutive_responses]": true, "tests/components/test_merge_json_data.py::test_merge_json_data[case3/left_data-case3/right_data-case3/output_data-None]": true, "tests/components/test_merge_llm_results.py::test_merge_json_data[case2/left_data-case2/right_data-case2/output_data-Conflict found for key 421F6E4674B6708A_5DE171CCD17E72F9_4_1]": true, "tests/components/test_merge_parsed_usr_data_and_meeting_data.py::test_merge_parsed_usr_data_and_meeting_data[left_input-right_input-expected_output-successful_merge1]": true, "tests/components/test_merge_parsed_usr_data_and_meeting_data.py::test_merge_parsed_usr_data_and_meeting_data[parsed_usr-meeting-parsed_usr-no_meeting_entities]": true, "tests/components/test_merge_selected_items.py::test_merge_selected_items_data[missing-meeting-name-left/left_data-missing-meeting-name-left/right_data-ans_predictor:preProcessingData:promptsInfo:prompt-preProcess-missing-meeting-name-left/output_data-meeting_name and question_id are None for left key 1]": true, "tests/components/test_merge_selected_items.py::test_merge_selected_items_data[missing-meeting-name-right/left_data-missing-meeting-name-right/right_data-ans_predictor:preProcessingData:promptsInfo:prompt-preProcess-missing-meeting-name-right/output_data-meeting_name and question_id are None for right key 1]": true, "tests/components/test_merge_selected_items.py::test_merge_selected_items_data[missing-selected-items/left_data-missing-selected-items/right_data-ans_predictor:preProcessingData:promptsInfo:prompt-preProcess-missing-selected-items/output_data-selected_items_values is None for key 1]": true, "tests/components/test_parse_recap_output.py::test_parse_meeting_recap[duplicate_meeting_key-Duplicate meeting key found:-None]": true, "tests/components/test_parse_recap_output.py::test_parse_meeting_recap[empty_display_text-Skipping empty displayText.-None]": true, "tests/components/test_parse_recap_output.py::test_parse_meeting_recap[empty_meeting-None-Skipping empty meeting key or meeting object.]": true, "tests/components/test_parse_recap_output.py::test_parse_meeting_recap[invalid_json-Invalid JSON in displayText: {\"KeyTopics\": [{\"Headline\": \"Topic 1\", \"Summary\": \"Summary 1\"-None]": true, "tests/components/test_parse_recap_output.py::test_parse_meeting_recap[invalid_json-Invalid JSON in displayText:-None]": true, "tests/components/test_parse_recap_output.py::test_parse_meeting_recap[invalid_json-None-None]": true, "tests/components/test_parse_recap_output.py::test_parse_meeting_recap[missing_fields-None-Skipping empty ans_predictor or ans_predictor[displayTextResults].]": true, "tests/components/test_parse_recap_output.py::test_parse_meeting_recap[missing_key_topics-Skipping empty KeyTopics.-None]": true, "tests/components/test_populate_prompt_core.py::test_get_prompt[prompts/generate_data_August24---]": true, "tests/components/test_populate_prompt_core.py::test_inject_from_jsonl_into_prompt[standard_data_generation/transcripts_gen/merge_inputs_async_sync_output/-meeting_minutes_from_agenda_item_December15-prompts/gen_meeting_minutes_item_prompt-4000-0-OutputPromptFormatType.LLM_API_ASYNC_CHAT_COMPLETION-False-None-None]": true, "tests/components/test_populate_prompt_core.py::test_inject_from_jsonl_into_prompt[test_O1/merge_inputs_async_sync_output/-meeting_minutes_from_agenda_item_December15-prompts-4000-0-OutputPromptFormatType.LLM_API_ASYNC_CHAT_COMPLETION-False-usr_meeting_minutes_data_generation/meeting_minutes_item/gen_prompt_async_output/-usr_meeting_minutes_data_generation/meeting_minutes_item/gen_prompt_async_prompt_output/]": true, "tests/components/test_populate_prompt_core.py::test_inject_from_jsonl_into_prompt[usr_data_generation/usr_data_gen/merge_inputs_async_sync_output/-meeting_minutes_from_agenda_item_December15-prompts-4000-0-OutputPromptFormatType.LLM_API_ASYNC_CHAT_COMPLETION-False-usr_meeting_minutes_data_generation/meeting_minutes_item/gen_prompt_async_output/-usr_meeting_minutes_data_generation/meeting_minutes_item/gen_prompt_async_prompt_output/]": true, "tests/components/test_populate_prompt_core.py::test_inject_from_jsonl_into_prompt[usr_data_generation/usr_data_gen/merge_inputs_async_sync_output/-meeting_minutes_from_agenda_item_December15-prompts-4000-0.0-OutputPromptFormatType.LLM_API_ASYNC_CHAT_COMPLETION-False-usr_meeting_minutes_data_generation/meeting_minutes_item/gen_prompt_async_output/-usr_meeting_minutes_data_generation/meeting_minutes_item/gen_prompt_async_prompt_output/]": true, "tests/components/test_populate_prompt_core.py::test_inject_from_jsonl_into_prompt[usr_data_generation/usr_data_gen/merge_inputs_async_sync_output/-meeting_minutes_from_agenda_item_December15-prompts-4000-0.5-llm_api_async_chat_completion-False-usr_meeting_minutes_data_generation/meeting_minutes_item/gen_prompt_async_output/-usr_meeting_minutes_data_generation/meeting_minutes_item/gen_prompt_async_prompt_output/]": true, "tests/components/test_populate_prompt_core.py::test_inject_from_jsonl_into_prompt_ft_pipeline[input_for_populate_the_prompt-generate_data_August24-prompts-4000-0-OutputPromptFormatType.FT-False-False-42-output-output_completion]": true, "tests/components/test_populate_prompt_core.py::test_inject_from_jsonl_into_prompt_ft_pipeline[input_for_populate_the_prompt-generate_data_August24.json-prompts-4000-0-OutputPromptFormatType.FT-False-False-42-output-output_completion]": true, "tests/components/test_populate_prompt_core.py::test_inject_from_jsonl_into_prompt_ft_pipeline[new/input-generate_agenda-new/prompts-4000-0-OutputPromptFormatType.FT-False-True-42-output-output_completion]": true, "tests/components/test_populate_prompt_core.py::test_inject_from_jsonl_into_prompt_ft_pipeline[populate_the_prompt_multi_scenario/input_for_populate_the_prompt--prompts-4000-0-OutputPromptFormatType.FT-False-True-42-populate_the_prompt_multi_scenario/output-populate_the_prompt_multi_scenario/output_completion]": true, "tests/components/test_populate_prompt_core.py::test_split_placeholder[placeholders0-placeholders_content0-]": true, "tests/components/test_stream_merger.py::test_merge_streams[enriched_stream_items2-1.0-1]": true, "tests/components/test_wrap_condition_function.py::test_wrap_condition_function[input_data-is_failed: False]": true, "tests/components/test_wrap_condition_function.py::test_wrap_condition_function_inner[case1/input_data-is_failed: true]": true, "tests/components/test_wrap_condition_function.py::test_wrap_condition_function_inner[case2/input_data-is_failed: false]": true, "tests/components/test_wrap_condition_function_inner.py::test_wrap_condition_function_inner[case1/input_data-is_failed: true]": true, "tests/components/test_wrap_condition_function_inner.py::test_wrap_condition_function_inner[case2/input_data-is_failed: false]": true, "tests/e2e/test_data_generation.py::test_e2e_agenda_data_generation_batched": true, "tests/e2e/test_vi_inent_generation.py::test_e2e_vi_intent_generation_async_sync[setup_test_data0]": true, "tests/tools/test_dict_helper.py::test_recursive_get_item[placeholder_content0-placeholder_split0-None]": true, "tests/tools/test_dict_helper.py::test_recursive_get_item[placeholder_content0-placeholder_split0-nan]": true, "tests/tools/test_dict_helper.py::test_sample_dict": true, "tests/tools/test_dict_helper.py::test_sample_dict_equal_types_distribution[data0-2-42-intent_types0-expected_output0]": true, "tests/tools/test_dict_helper.py::test_sample_dict_equal_types_distribution[data2-5-42-intent_types2-expected_output2]": true, "tests/tools/test_llm_utilities.py::test_extract_parsed_llm_output[item3-3-dialogue-LLMOutputFormatType.DIALOGUE_STRING-expected_output3-log_messages3]": true, "tests/tools/test_llm_utilities.py::test_parse_json_content[```json\\n{\\n    \"meeting_transcript\": {\\n        \"1\": {\\n            \"speaker\": \"Alice\",\\n            \"text\": \"So, um, Frank, I think you made a really good point about needing to get a handle on the budget early on. We can't really move forward without knowing what our financial constraints are, right?\"\\n        },\\n        \"2\": {\\n            \"speaker\": \"Frank\",\\n            \"text\": \"Yeah, exactly. I mean, if we don't have a preliminary budget, we won't know what kind of resources we can allocate to different parts of the expo.\"\\n        },\\n        \"3\": {\\n            \"speaker\": \"Alice\",\\n            \"text\": \"Right, right. So, um, I was thinking, maybe we should start drafting a preliminary budget. It doesn't have to be perfect, just something to give us a ballpark figure.\"\\n        },\\n        \"4\": {\\n            \"speaker\": \"Frank\",\\n            \"text\": \"Yeah, I can definitely work on that. I have some experience with budgeting from the last event we organized.\"\\n        },\\n        \"5\": {\\n            \"speaker\": \"Alice\",\\n            \"text\": \"Oh, that's great! Do you think you could have a draft ready by, say, next week? That way we can review it in our next meeting.\"\\n        },\\n        \"6\": {\\n            \"speaker\": \"Frank\",\\n            \"text\": \"Uh, yeah, I think that's doable. I'll start by listing out the major expenses like venue, marketing, and, um, maybe some contingency funds.\"\\n        },\\n        \"7\": {\\n            \"speaker\": \"Alice\",\\n            \"text\": \"Sounds good. And, um, if you need any help, just let me know. I can assist with gathering quotes or any other details you might need.\"\\n        },\\n        \"8\": {\\n            \"speaker\": \"Frank\",\\n            \"text\": \"Thanks, Alice. I might take you up on that. Oh, and should we also consider potential sponsorships in the budget?\"\\n        },\\n        \"9\": {\\n            \"speaker\": \"Alice\",\\n            \"text\": \"Oh, definitely. Sponsorships could really help offset some of the costs. Maybe we should brainstorm a list of potential sponsors too.\"\\n        },\\n        \"10\": {\\n            \"speaker\": \"Frank\",\\n            \"text\": \"Yeah, good idea. I'll add a section for that in the budget draft.\"\\n        },\\n        \"11\": {\\n            \"speaker\": \"Alice\",\\n            \"text\": \"Perfect. And, um, once we have the draft, we can start reaching out to those sponsors.\"\\n        },\\n        \"12\": {\\n            \"speaker\": \"Frank\",\\n            \"text\": \"Absolutely. I'll get started on this right away.\"\\n        },\\n        \"13\": {\\n            \"speaker\": \"Alice\",\\n            \"text\": \"Great, thanks, Frank. This is really coming together.\"\\n        },\\n        \"14\": {\\n            \"speaker\": \"Grace\",\\n            \"text\": \"So, um, I wanted to bring up the need for marketing and promotion for the Community Renewable Energy Expo. I think we should really focus on using social media, local newspapers, and community boards to spread the word.\"\\n        },\\n        \"15\": {\\n            \"speaker\": \"John\",\\n            \"text\": \"Yeah, Grace, I totally agree. Social media is a must. But, uh, do we have someone who can handle that? I mean, like, creating posts and managing the accounts?\"\\n        },\\n        \"16\": {\\n            \"speaker\": \"Grace\",\\n            \"text\": \"Good point, John. I was thinking maybe we could get some of the students involved. They\\u2019re pretty savvy with social media, and it could be a good learning experience for them.\"\\n        },\\n        \"17\": {\\n            \"speaker\": \"Sarah\",\\n            \"text\": \"That\\u2019s a great idea, Grace. We could even make it a project for the marketing class. They could come up with a campaign and everything.\"\\n        },\\n        \"18\": {\\n            \"speaker\": \"John\",\\n            \"text\": \"Oh, that\\u2019s brilliant, Sarah. And for the local newspapers, do we have any contacts? I mean, who\\u2019s gonna write the press release?\"\\n        },\\n        \"19\": {\\n            \"speaker\": \"Grace\",\\n            \"text\": \"I can draft something up. I\\u2019ve done it before, so it shouldn\\u2019t be too hard. But we\\u2019ll need someone to follow up with the newspapers to make sure they actually publish it.\"\\n        },\\n        \"20\": {\\n            \"speaker\": \"Sarah\",\\n            \"text\": \"I can take care of that. I know a couple of people at the Gazette.\"\\n        },\\n        \"21\": {\\n            \"speaker\": \"John\",\\n            \"text\": \"Awesome. And what about the community boards? Do we just, like, print out flyers and post them around town?\"\\n        },\\n        \"22\": {\\n            \"speaker\": \"Grace\",\\n            \"text\": \"Yeah, pretty much. We can also ask local businesses if they\\u2019d be willing to put up a flyer in their windows.\"\\n        },\\n        \"23\": {\\n            \"speaker\": \"Sarah\",\\n            \"text\": \"Maybe we could also reach out to community centers and libraries. They usually have bulletin boards for events like this.\"\\n        },\\n        \"24\": {\\n            \"speaker\": \"John\",\\n            \"text\": \"Sounds like a plan. So, to recap, Grace will handle the social media with the students, Sarah will follow up with the newspapers, and we\\u2019ll all pitch in with the flyers.\"\\n        },\\n        \"25\": {\\n            \"speaker\": \"Grace\",\\n            \"text\": \"Exactly. And let\\u2019s not forget to keep track of everything. We should have a shared document or something to make sure we\\u2019re all on the same page.\"\\n        },\\n        \"26\": {\\n            \"speaker\": \"Sarah\",\\n            \"text\": \"I can set up a Google Doc for that.\"\\n        },\\n        \"27\": {\\n            \"speaker\": \"John\",\\n            \"text\": \"Perfect. Thanks, Sarah.\"\\n    }\\n}\\n```-aa]": true, "tests/tools/test_llm_utilities.py::test_parse_json_content[```json\\n{\\n    \"meeting_transcript\": {\\n        \"1\": {\\n            \"speaker\": \"Alice\",\\n            \"text\": \"So, um, Frank, I think you made a really good point about needing to get a handle on the budget early on. We can't really move forward without knowing what our financial constraints are, right?\"\\n        },\\n        \"2\": {\\n            \"speaker\": \"Frank\",\\n            \"text\": \"Yeah, exactly. I mean, if we don't have a preliminary budget, we won't know what kind of resources we can allocate to different parts of the expo.\"\\n        }\\n}\\n```-aa]": true}