"""
Sample the user intent data based on the number of intents and seed using streaming operations to avoid OOM issues
"""

import logging
from tools.dict_helpers import sample_dict_equal_types_distribution
from tools.io_helpers import (
    read_all_files_of_json_iterator,
    write_file_to_storage_with_streaming,
    make_json_serializable,
    list_file_type_in_path,
)


def sample_user_intent(
    input_path: str,
    column_name: str,
    filtered_column_name: str,
    nums_of_intents_per_type: int,
    seed: int,
    output_path: str,
    intent_types: list = ["explicit", "implicit"],
    logger: logging.Logger = None,
    chunk_size: int = 1000,
):
    """
    Sample the user intent data based on the number of intents per type and seed using streaming operations.
    This version processes the data in chunks to avoid OOM issues while maintaining backward compatibility.

    :param input_path: The path to the input data
    :param column_name: The name of the column to sample
    :param filtered_column_name: The name of the column to write the sampled data to
    :param nums_of_intents_per_type: The number of intents to sample per intent type
    :param seed: The seed to use for sampling
    :param output_path: The path to the output data
    :param intent_types: The types of intents to sample
    :param logger: The logger to use for logging
    :param chunk_size: Size of chunks for streaming processing
    """
    logger.info(
        f"sample_user_intent: processing user intent data from: {input_path} with streaming"
    )

    # Check if input path exists and has JSON files
    json_files = list_file_type_in_path(input_path, "json", logger)
    if not json_files:
        logger.error(f"No JSON files found in {input_path}")
        raise ValueError(f"No JSON files found in {input_path}")

    total_records = 0
    processed_records = 0
    has_data = False
    column_found = False

    def process_data_generator():
        """Generator that processes data in chunks and yields filtered results"""
        nonlocal total_records, processed_records, has_data, column_found

        try:
            # Use the streaming iterator to read JSON files in chunks
            for df_chunk in read_all_files_of_json_iterator(
                input_path, "json", logger, chunk_size
            ):
                if df_chunk.empty:
                    continue

                has_data = True
                total_records += len(df_chunk)

                # Check if required column exists in this chunk
                if column_name not in df_chunk.columns:
                    logger.error(f"Column '{column_name}' not found in DataFrame")
                    raise ValueError(f"Column '{column_name}' not found in DataFrame")

                column_found = True

                # Process each row in the chunk
                chunk_results = {}
                for idx, row in df_chunk.iterrows():
                    try:
                        # Sample the user intent data for this row
                        filtered_data = sample_dict_equal_types_distribution(
                            row[column_name],
                            nums_of_intents_per_type,
                            seed,
                            intent_types,
                            logger,
                        )

                        # Create result row with original data plus filtered column
                        result_row = row.to_dict()
                        result_row[filtered_column_name] = filtered_data

                        # Make the result JSON serializable
                        chunk_results[str(idx)] = make_json_serializable(result_row)

                    except Exception as e:
                        logger.error(f"Error processing row {idx}: {e}")
                        continue

                processed_records += len(chunk_results)

                if chunk_results:
                    yield chunk_results

        except Exception as e:
            # Re-raise the original exception to maintain backward compatibility
            raise e

    # Check for empty input by trying to process first
    try:
        data_generator = process_data_generator()
        # Consume the generator to check if we have data
        all_results = list(data_generator)

        # Check if we actually got any data
        if not has_data:
            logger.info("input dataframe is empty")
            raise ValueError("input dataframe is empty")

        # Log the expected messages for backward compatibility
        logger.info(f"filter_user_intent: number of total records {total_records}")
        logger.info(
            f"filter_user_intent: number of records with {column_name} {total_records}"
        )
        logger.info(
            f"filter_user_intent: number of records with {filtered_column_name} {total_records} after filtering."
        )

        # Write results using streaming
        def result_generator():
            for result_chunk in all_results:
                yield result_chunk

        write_file_to_storage_with_streaming(
            output_path=output_path,
            filename="merged_output.json",
            logger=logger,
            data_iterator=result_generator(),
        )

        logger.info("wrote merged_data")

    except ValueError as e:
        # Re-raise ValueError with original message format
        raise e
    except Exception as e:
        logger.error(f"Error in streaming processing: {e}")
        raise
