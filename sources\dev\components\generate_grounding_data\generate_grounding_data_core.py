"""
Core implementation for generating and processing meeting grounding data.

This module provides the core functionality for generating, transforming, and managing
meeting grounding data. It includes:
- Reading and validating meeting transcript data
- Converting chat messages to standardized Polymer format
- Generating grounding data from meeting transcripts
- Handling both transcript-only and transcript-with-chat scenarios
- Managing data format transformations and validation
- Error handling and detailed logging of processing status
- Statistics tracking for missing data and processing ratios

The module serves as the backend implementation for the Azure ML component interface,
focusing on data processing and format standardization for meeting analysis.
"""

import logging
import random
from datetime import datetime, timedelta, timezone

from tools.io_helpers import read_file_in_path, write_file_to_storage
from tools.polymer_client.polymer_helper import generate_grounding_data_from_transcript


def convert_chat_data_to_polymer_format(chat_messages: list,
                                      id: str,
                                      meeting_duration_minutes: int,
                                      seed: int = None,
                                      logger: logging.Logger = None,
                                      ):
    """
    Convert chat data to polymer format
    :param chat_messages: The chat data
    :param id: The id of the chat data
    :param meeting_duration_minutes: meeting duration in minutes
    :param seed: Optional random seed for reproducible results
    :param logger: the logger
    """
    logger.info("Converting chat data to polymer format")

    conversation_id = f"{id}_chat"

    chat_data = {
        "conversation_id": conversation_id,
        "dialogue": [],
        "participants": [],
    }
    participants = set()

    if not isinstance(chat_messages, list):
        logger.error("Chat messages must be a list")
        raise ValueError("Chat messages must be a list")

    try:
        # Set random seed if provided for reproducible results
        if seed is not None:
            random.seed(seed)
            logger.info(f"Using random seed: {seed}")

        # randomly generate meeting time
        year = random.randint(2023, 2025)
        month = random.randint(1, 12)
        day = random.randint(1, 28)
        hour = random.randint(9, 16)
        minute = random.randint(0, 59)

        # generate meeting start and end time
        meeting_start = datetime(year, month, day, hour, minute, 0, tzinfo=timezone.utc)
        meeting_end = meeting_start + timedelta(minutes=meeting_duration_minutes)

        # initialize current times for before, during, and after the meeting
        # before: meeting starts 24 hours before
        # during: meeting starts
        # after: meeting ends 24 hours after
        current_times = {
            "before": meeting_start - timedelta(hours=24),
            "during": meeting_start,
            "after": meeting_end
        }

        time_limits = {
            "before": meeting_start,                       # cannot exceed meeting start
            "during": meeting_end,                         # cannot exceed meeting end
            "after": meeting_end + timedelta(hours=24)      # cannot exceed 24 hours after meeting end
        }

        logger.info(f"Generated meeting time: {meeting_start.isoformat().replace('+00:00', 'Z')}")

    except Exception as e:
        logger.error(f"Failed to generate meeting time: {str(e)}")
        current_times = {}
        time_limits = {}

    for index, message in enumerate(chat_messages):
        dialogue_item = {}
        dialogue_item["id"] = message.get("id", index)
        dialogue_item["userName"] = message.get("sender", "unknown")
        dialogue_item["message"] = "<p>" + message.get("content", "") + "</p>"
        dialogue_item["attachments"] = message.get("attachments", [])
        dialogue_item["hasAttachments"] = True if len(dialogue_item["attachments"]) > 0 else False
        dialogue_item["replyTo"] = message.get("reply_to", None)

        # generate time period for the message
        if current_times:
            time_period = message.get("time_period", "during")

            if time_period == "during":
                interval = random.randint(1, max(2, meeting_duration_minutes // len(chat_messages)))
            else:
                interval = random.randint(1, 5)

            current_times[time_period] += timedelta(minutes=interval)

            if current_times[time_period] >= time_limits[time_period]:
                current_times[time_period] = time_limits[time_period] - timedelta(minutes=1)

            dialogue_item["time"] = current_times[time_period].isoformat().replace('+00:00', 'Z')
        else:
            dialogue_item["time"] = None

        dialogue_item["isRead"] = True
        participants.add(dialogue_item["userName"])
        chat_data["dialogue"].append(dialogue_item)

    chat_data["participants"] = sorted(list(participants))

    return chat_data

def generate_grounding_data_inner(
    input_path: str,
    file_name: str,
    transcript_key: str,
    meeting_grounding_file_name: str,
    output_path: str,
    chat_key: str = None,
    seed: int = 42,
    logger: logging.Logger = None,
):
    """
    Generate grounding data
    :param input_path: The path to the grounding data
    :param file_name: The name of the grounding data file
    :param transcript_key: The key of the transcript to use
    :param meeting_grounding_file_name: The name of the meeting grounding data
    :param output_path: the output path of the info file containing the transcript and completion
    :param chat_key: The key of the chat messages to use
    :param logger: the logger
    :param seed: Optional random seed for reproducible results
    """

    logger.info(
        f"Generating grounding data for generating polymer response for transcripts {file_name} from folder {input_path}",
    )

    input_meetings_grounding = read_file_in_path(input_path, file_name, logger)
    if input_meetings_grounding is None:
        raise ValueError(f"Transcript {file_name} not found")
    elif not isinstance(input_meetings_grounding, dict):
        raise ValueError(f"Content must be a dict for JSON format")
    else:
        if len(input_meetings_grounding) == 0:
            raise ValueError(f"Transcript {file_name} is empty")

    logger.info(
        f"Input transcripts count {len(input_meetings_grounding)}",
    )

    if chat_key is None:
        logger.info(f"Chat key is None")

    meeting_grounding = {}
    missing_chat_key = []
    missing_transcript_key = []
    for key, item in input_meetings_grounding.items():
        transcript = item.get(transcript_key, None)
        if not transcript or not isinstance(transcript, dict):
            missing_transcript_key.append(key)
            continue
        grouding_data = generate_grounding_data_from_transcript(key, transcript, logger)
        grouding_data["id"] = key
        chat_messages = item.get(chat_key, None)
        if not chat_key:
            meeting_grounding[key] = grouding_data
            continue

        transcript_args = item.get('transcript_args', None)
        meeting_duration_minutes = transcript_args.get('meeting_length_min', 60) if transcript_args else 60

        if not chat_messages or not isinstance(chat_messages, list):
            missing_chat_key.append(key)
        else:
            grouding_data["chat_data"] = convert_chat_data_to_polymer_format(chat_messages, key, meeting_duration_minutes, seed=seed, logger=logger)
            grouding_data["is_chat_scenario"] = False
        meeting_grounding[key] = grouding_data


    if len(missing_chat_key):
        logger.info(f"Chat key {chat_key} not found for {missing_chat_key}")
        meeting_withotut_chat_ratio = len(missing_chat_key) / len(input_meetings_grounding)
        logger.info("meeting_without_chat_ratio ratio: %s", meeting_withotut_chat_ratio)

    if len(missing_transcript_key):
        logger.info(f"Transcript key {transcript_key} not found for {missing_transcript_key}")
        meeting_without_transcript_ratio = len(missing_transcript_key) / len(input_meetings_grounding)
        logger.info("meeting_without_transcript_ratio ratio: %s", meeting_without_transcript_ratio)


    write_file_to_storage(
        meeting_grounding,
        output_path,
        meeting_grounding_file_name,
        logger,
        "json",
    )
