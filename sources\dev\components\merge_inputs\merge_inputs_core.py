"""
Core implementation for merging input data from multiple sources.

This module provides functionality to:
- Merge structured data from different input sources
- Handle key conflicts with customizable resolution strategies
- Apply field renaming through override mappings
- Process hierarchical data structures with varying key depths
- Manage data transformation between DataFrames and dictionaries
- Skip invalid or missing values during merge operations
- Create unified output from disparate data sources
- Maintain data integrity with conflict detection and resolution

The module implements sophisticated merging logic that preserves data
hierarchies while providing options for conflict resolution through
suffix-based naming conventions or strict validation.
"""

import logging
import math
from typing import Dict, Optional

import pandas as pd

from tools.io_helpers import (
    read_all_files_of_type,
    read_all_files_of_json_iterator,
    write_file_to_storage_with_streaming,
)
from tools.merge_utils import parse_override_string


def merge_data_with_overrides(
    source_data: Dict,
    merged: Dict,
    override: Optional[Dict[str, str]],
    suffix: str,
    suffix_conflicts: bool,
    merged_keys_len: int,
    current_key_len: int,
    logger: logging.Logger,
):
    """
    Merge data from source_data into the merged dictionary, applying overrides and handling conflicts.

    This function handles hierarchical data structures and merges them appropriately based on key depths.
    It applies field renaming through overrides, handles key conflicts by either adding suffixes or
    raising errors, and skips invalid values like None or NaN during the merge process.

    :param source_data: The data to merge (from "left" or "right") in dictionary format.
    :param merged: The merged dictionary to update with source_data content.
    :param override: Field renaming overrides mapping original field names to new names.
    :param suffix: Suffix to apply for conflicting keys (e.g., 'left' or 'right').
    :param suffix_conflicts: Whether to add suffixes for conflicting keys or raise an error.
    :param merged_keys_len: The maximum key depth in the merged dictionary.
    :param current_key_len: The key depth in the current source_data.
    :param logger: Logger instance for recording operations and errors.
    """
    logger.info(
        f"Starting merge with source_data keys: {list(source_data.keys())}",
    )
    if current_key_len < merged_keys_len:
        alternative_merged_keys = set(
            [
                "_".join(merged_key.split("_")[:current_key_len])
                for merged_key in merged.keys()
            ]
        )

    for root_key, content in source_data.items():
        logger.debug(f"Processing root_key: {root_key}")
        if (current_key_len >= merged_keys_len and root_key not in merged) or (
            current_key_len < merged_keys_len
            and root_key not in alternative_merged_keys
        ):
            merged[root_key] = {}
            logger.debug(
                f"Added new root_key: {root_key} to merged.",
            )

        for field_key, value in content.items():
            original_field_key = field_key
            if override and field_key in override:
                field_key = override[field_key]
                logger.info(
                    f"Field '{original_field_key}' overridden to '{field_key}'.",
                )

            # Ignore `nan` values
            if value is None or (isinstance(value, float) and math.isnan(value)):
                logger.warning(
                    f"Skipping field '{field_key}' with value 'nan'.",
                )
                continue

            if (
                current_key_len >= merged_keys_len and field_key in merged[root_key]
            ):  # same hierarchy keys, current field key is in merged
                add_content_to_root_key_while_field_key_exist(
                    merged, suffix, suffix_conflicts, logger, root_key, field_key, value
                )

            elif current_key_len < merged_keys_len:  # Different hierarchy keys
                for merged_key in merged.keys():
                    if root_key in merged_key:
                        if (
                            field_key in merged[merged_key]
                        ):  # Different hierarchy keys, current field key is in merged
                            add_content_to_root_key_while_field_key_exist(
                                merged,
                                suffix,
                                suffix_conflicts,
                                logger,
                                merged_key,
                                field_key,
                                value,
                            )
                        else:  # Different hierarchy keys, current field key NOT is in merged
                            merged[merged_key][field_key] = value
                logger.debug(
                    f"Added field '{field_key}' with value '{value}' to root_key '{root_key}'.",
                )

            else:  # Same hierarchy keys, current field key is NOT in merged
                merged[root_key][field_key] = value
                logger.debug(
                    f"Added field '{field_key}' with value '{value}' to root_key '{root_key}'.",
                )

    logger.info(
        f"Merge complete. Merged data keys: {list(merged.keys())}",
    )


def add_content_to_root_key_while_field_key_exist(
    merged, suffix, suffix_conflicts, logger, root_key, field_key, value
):
    """
    Add content to a specific root key in the merged dictionary when the field key already exists.

    This function handles conflict resolution for duplicate field keys, either by adding
    a suffix to the field name or raising an error based on the suffix_conflicts parameter.

    :param merged: The merged dictionary to update.
    :param suffix: The suffix to apply to conflicting field keys (e.g., 'left', 'right').
    :param suffix_conflicts: Boolean indicating whether to handle conflicts by adding suffixes.
    :param logger: Logger instance for recording operations and errors.
    :param root_key: The root key in the merged dictionary to update.
    :param field_key: The field key that has a conflict.
    :param value: The value to assign to the field key.
    :raises ValueError: If suffix_conflicts is False and a conflict is detected.
    """
    if suffix_conflicts:
        conflict_field_key = f"{field_key}_{suffix}"
        merged[root_key][conflict_field_key] = value
        logger.warning(
            f"Conflict detected on field '{field_key}'. Added field '{conflict_field_key}' with value '{value}'.",
        )
    else:
        logger.error(
            f"Conflict detected on field '{field_key}' without suffix handling enabled.",
        )
        raise ValueError(f"Conflict found on key '{field_key}' without suffix policy.")


def get_max_key_length(left_data: Dict, right_data: Dict) -> tuple[int, int, int]:
    """
    Get the maximum key length from two dictionaries. Key length is determined by the number of underscores + 1.

    This function computes the maximum depth of keys in hierarchical dictionaries by counting
    underscore separators, which is crucial for determining how to properly merge data with
    different hierarchy levels.

    :param left_data: The left dictionary to analyze.
    :param right_data: The right dictionary to analyze.
    :return: A tuple of (max_key_length, left_key_length, right_key_length) containing:
             - The overall maximum key length across both dictionaries
             - The maximum key length in the left dictionary
             - The maximum key length in the right dictionary
    """
    left_max_key_len = max([len(str(key).split("_")) for key in left_data.keys()])
    right_max_key_len = max([len(str(key).split("_")) for key in right_data.keys()])
    return max(left_max_key_len, right_max_key_len), left_max_key_len, right_max_key_len


def merge_dataframes_to_dict(
    left_df: pd.DataFrame,
    right_df: pd.DataFrame,
    left_override: Optional[Dict[str, str]] = None,
    right_override: Optional[Dict[str, str]] = None,
    suffix_conflicts: bool = True,
    logger: logging.Logger = None,
) -> Dict:
    """
    Merge two pandas DataFrames into a dictionary, handling conflicts and overrides.

    This function converts two DataFrames to dictionaries and then merges them using a sophisticated
    strategy that handles hierarchical structures and key conflicts. The merging process:
    1. Converts DataFrames to dictionaries with hierarchical keys
    2. Determines the maximum key depth in both dictionaries
    3. Merges the dictionary with longer keys first (to preserve hierarchy)
    4. Applies field renaming through override mappings
    5. Resolves conflicts using suffixes if enabled
    6. Updates request metadata with unique keys

    :param left_df: DataFrame from the "left" input folder to be merged.
    :param right_df: DataFrame from the "right" input folder to be merged.
    :param left_override: Optional field renaming mapping for the "left" data fields.
    :param right_override: Optional field renaming mapping for the "right" data fields.
    :param suffix_conflicts: Whether to add '_left' and '_right' suffixes for conflicting keys.
                            If False, conflicts will raise ValueError.
    :param logger: Logger instance for recording operations and errors.
    :return: Merged dictionary with root keys preserved and conflicts resolved according to settings.
    """
    merged = {}

    logger.info("Converting DataFrames to dictionaries...")

    # Convert DataFrames to dictionaries with key structure
    # Ensure all keys are strings for consistent processing
    left_data_raw = left_df.to_dict(orient="index")
    right_data_raw = right_df.to_dict(orient="index")

    # Convert all keys to strings to ensure consistency
    left_data = {str(k): v for k, v in left_data_raw.items()}
    right_data = {str(k): v for k, v in right_data_raw.items()}
    max_keys_len, left_key_len, right_key_len = get_max_key_length(
        left_data, right_data
    )

    # merge the longer key first (if the key length is the same, merge the left data first).
    # Longer key necessarily means usage of ".item", i.e. separating of previous element (second data) to multiple elements
    if left_key_len >= right_key_len:
        first_data = left_data
        first_key_len = left_key_len
        second_data = right_data
        second_key_len = right_key_len
    else:
        first_data = right_data
        first_key_len = right_key_len
        second_data = left_data
        second_key_len = left_key_len

    # Merge first data
    merge_data_with_overrides(
        source_data=first_data,
        merged=merged,
        override=left_override,
        suffix="left",
        suffix_conflicts=suffix_conflicts,
        merged_keys_len=max_keys_len,
        current_key_len=first_key_len,
        logger=logger,
    )

    # Merge "right" data
    merge_data_with_overrides(
        source_data=second_data,
        merged=merged,
        override=right_override,
        suffix="right",
        suffix_conflicts=suffix_conflicts,
        merged_keys_len=max_keys_len,
        current_key_len=second_key_len,
        logger=logger,
    )
    # Update request_metadata with the unique key
    for key, item in merged.items():
        if "request_metadata" in item:
            merged[key]["request_metadata"] = str(key)
    return merged


def merge_inputs_inner(
    left_input_path: str,
    right_input_path: str,
    left_override_str: str,
    right_override_str: str,
    file_type: str,
    output_path: str,
    logger: logging.Logger,
    suffix_conflicts: bool = True,
    output_file_name: str = "merged_output.json",
    chunk_size: int = 1000,
) -> None:
    """
    Merge inputs from two paths with optional key overrides and save the result to a file.

    This function orchestrates the complete end-to-end merging process using streaming techniques
    to handle large datasets without OOM issues:
    1. Reads small right data into memory completely
    2. Streams large left data in chunks using iterator
    3. Merges each chunk with the right data
    4. Writes the merged result using streaming JSON writer

    The function handles validation, error reporting, and ensures that the entire
    pipeline from reading source files to generating the final merged output is
    executed properly while managing memory efficiently.

    :param left_input_path: The path to the folder containing left input files (large data).
    :param right_input_path: The path to the folder containing right input files (small data).
    :param left_override_str: Override mapping string for keys in the left input (format: "original1:new1,original2:new2").
    :param right_override_str: Override mapping string for keys in the right input (format: "original1:new1,original2:new2").
    :param file_type: The type of files to read (e.g., "json").
    :param output_path: The path to save the merged output.
    :param logger: Logger instance for recording operations and errors.
    :param suffix_conflicts: Whether to add suffixes to resolve key conflicts (True) or raise errors (False).
    :param output_file_name: The name of the output file. Defaults to "merged_output.json".
    :param chunk_size: Size of chunks for streaming processing. Defaults to 1000.
    :raises ValueError: If input data cannot be loaded or another error occurs during processing.
    """
    # Read right data (small) into memory completely
    logger.info("Reading right input data (small dataset)...")
    right_data = read_all_files_of_type(right_input_path, file_type, logger)

    if right_data is None:
        logger.error("Error loading right input data.")
        raise ValueError("Failed to load right input data.")

    # Convert override strings to dictionaries
    logger.info("Converting override strings to dictionaries...")
    left_override = parse_override_string(left_override_str, logger)
    right_override = parse_override_string(right_override_str, logger)

    # Get left data iterator (large data)
    logger.info("Setting up left input data streaming iterator (large dataset)...")
    left_data_iterator = read_all_files_of_json_iterator(
        left_input_path, file_type, logger, chunk_size
    )

    def merge_chunks_generator():
        """Generator that yields merged chunks to avoid loading all data into memory"""
        any_data_processed = False

        for left_chunk_df in left_data_iterator:
            logger.info(f"Processing left data chunk with shape: {left_chunk_df.shape}")

            if left_chunk_df is None or left_chunk_df.empty:
                logger.warning("Empty left data chunk, skipping...")
                continue

            # Perform the merging for this chunk
            logger.debug("Merging current left chunk with right data...")
            try:
                chunk_merged_data = merge_dataframes_to_dict(
                    left_df=left_chunk_df,
                    right_df=right_data,
                    left_override=left_override,
                    right_override=right_override,
                    suffix_conflicts=suffix_conflicts,
                    logger=logger,
                )
            except ValueError as e:
                # Re-raise ValueError from merging (e.g., conflict without suffix policy)
                logger.error(f"Error during merge: {e}")
                raise

            any_data_processed = True
            yield chunk_merged_data

        if not any_data_processed:
            logger.error("No data processed from left input.")
            raise ValueError("No data processed from left input.")

    # Save the merged result using streaming writer
    logger.info("Saving merged data using streaming writer...")
    try:
        write_file_to_storage_with_streaming(
            output_path=output_path,
            filename=output_file_name,
            logger=logger,
            data_iterator=merge_chunks_generator(),
        )
    except ValueError as e:
        # Propagate ValueError from generator (e.g., merge conflicts)
        logger.error(f"Error during streaming write: {e}")
        raise
