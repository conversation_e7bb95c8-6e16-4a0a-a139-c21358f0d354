"""
Read the json files of the output with intents and process them into readable format by reducing the schema.
"""

import logging
from collections import defaultdict
from typing import List
from tools.io_helpers import (
    write_file_to_storage_with_streaming,
    read_all_files_of_json_iterator,
    make_json_serializable,
)


def merge_intents_data(
    input_path: str,
    file_name: str,
    output_path: str,
    logger: logging.Logger,
    merge_fields: List[str] = ["single_intents"],
    chunk_size: int = 1000,
):
    """
    Read the json files of the output with intents and process them into readable format by reducing the schema using streaming.
    This version processes the data in chunks to avoid OOM issues.

    :param input_path: The path to the json file
    :param file_name: the name of the json file
    :param output_path: The path to save the readable merged transcripts
    :param logger: The logger
    :param merge_fields: The fields to merge
    :param chunk_size: Size of chunks for streaming processing
    """
    logger.info(
        f"merge_intents_data: reading Polymer responses data json from: {input_path} with streaming"
    )

    # Dictionary to store merged results
    merged_data = defaultdict(lambda: {field: {} for field in merge_fields})
    total_records = 0
    processed_records = 0

    def process_data_generator():
        """Generator that processes data in chunks and yields merged results"""
        nonlocal total_records, processed_records

        # Use the existing streaming iterator
        for df_chunk in read_all_files_of_json_iterator(
            input_path, "json", logger, chunk_size
        ):
            # Convert DataFrame back to dict format for processing
            chunk_data = df_chunk.T.to_dict()
            total_records += len(chunk_data)

            # Process this chunk
            process_chunk(chunk_data, merged_data, merge_fields)
            processed_records += len(chunk_data)
            logger.info(f"Processed {processed_records}/{total_records} records")

        logger.info(
            f"merge_intents_data: number of total records after merging {len(merged_data)}"
        )
        yield merged_data

    # Use streaming writer
    write_file_to_storage_with_streaming(
        output_path=output_path,
        filename=file_name,
        logger=logger,
        data_iterator=process_data_generator(),
    )


def process_chunk(chunk_data: dict, merged_data: dict, merge_fields: List[str]):
    """Process a chunk of data and merge it into the merged_data dictionary
    :param chunk_data: The chunk of data to process
    :param merged_data: The merged data dictionary to update
    :param merge_fields: The fields to merge
    """
    for key, value in chunk_data.items():
        # Extract parent ID (remove last _X)
        parent_id = "_".join(key.split("_")[:-1])

        # Extract last index from the key
        index = key.split("_")[-1]

        # Store shared fields only once per parent ID
        for field in value:
            if field not in merge_fields and field not in merged_data[parent_id]:
                merged_data[parent_id][field] = value[field]

        # Merge specified fields under their respective structures
        for field in merge_fields:
            if field in value:
                merged_data[parent_id][field][index] = value[field]

    return
