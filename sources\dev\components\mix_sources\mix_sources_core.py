"""
Mix Sources Core Module.

This module provides functionality for mixing meeting data with intents data to generate
query examples for different meeting grounding sources (transcript, chat, vi),
while ensuring non per‐meeting duplication of intents.
It handles data filtering, randomization, and batch processing to create balanced sets
of queries for each data source.

The module supports processing of different types of queries:
- Transcript queries: Selected from intents with empty grounding sources
- Chat queries: Mixed from explicit (empty grounding) and implicit (chat/links grounding) intents
- VI queries: Mixed from explicit (empty grounding) and implicit (VI grounding) intents
"""

import logging
import pandas as pd
from typing import List, Dict, Any, Optional

from tools.pd_df_helpers import get_column_by_name
from tools.io_helpers import (
    read_all_files_of_type,
    write_file_to_storage_with_streaming,
    read_all_files_of_json_iterator,
)


def filter_on_intents(
    intents_data: pd.DataFrame,
    intents: List[str] = ["Ask/Retrieve", "Recap", "Compose"],
) -> pd.DataFrame:
    """
    Filter the intents data to include only rows with specified intents.

    :param intents_data: DataFrame with intents data.
    :param intents: List of intents to include. Defaults to ["Ask/Retrieve", "Recap", "Compose"].
    :return: The filtered DataFrame.
    """
    if "intent" not in intents_data.columns.str.lower():
        raise ValueError("No 'intent' column found in the intents data.")
    filtered = intents_data[get_column_by_name(intents_data, "intent").notnull()]
    filtered = filtered[get_column_by_name(filtered, "intent").isin(intents)]
    return filtered


def filter_on_context_dependant(
    intents_data: pd.DataFrame, col_name: str = "Meeting context dependant"
) -> pd.DataFrame:
    """
    Filter the intents data to include only rows where the specified context dependent column is valid.

    :param intents_data: DataFrame with intents data.
    :param col_name: Name of the context dependent column. Defaults to "Meeting context dependant".
    :return: The filtered DataFrame.
    """
    if col_name.lower() not in intents_data.columns.str.lower():
        raise ValueError(f"No {col_name} column found in the intents data.")
    try:
        filtered = intents_data[get_column_by_name(intents_data, col_name).notnull()]
        filtered = filtered[
            (get_column_by_name(filtered, col_name) == True)
            | (get_column_by_name(filtered, col_name).str.lower() == "true")
            | (get_column_by_name(filtered, col_name).str.lower() == "y")
            | (get_column_by_name(filtered, col_name).str.lower() == "yes")
        ]
    except ValueError:
        raise ValueError("No 'context_dependent' column found in the intents data.")
    return filtered


def filter_on_empty_grounding(
    intents_data: pd.DataFrame, column_name: str = "Explicit grounding source"
) -> pd.DataFrame:
    """
    Filter rows in the intents data where the grounding column is empty (null or whitespace).

    :param intents_data: DataFrame with intents data.
    :param column_name: Column name indicating grounding source. Defaults to "Explicit grounding source".
    :return: The filtered DataFrame.
    """
    grounding_col = get_column_by_name(intents_data, column_name)
    return intents_data[
        grounding_col.isnull() | (grounding_col.astype(str).str.strip() == "")
    ]


def filter_by_grounding_values(
    intents_data: pd.DataFrame,
    values: List[str],
    column_name: str = "Explicit grounding source",
) -> pd.DataFrame:
    """
    Filter rows in the intents data where the grounding column (after lowercasing and stripping) is in the provided values.

    :param intents_data: DataFrame with intents data.
    :param values: List of acceptable grounding values.
    :param column_name: Column name indicating grounding source. Defaults to "Explicit grounding source".
    :return: The filtered DataFrame.
    """
    grounding_col = get_column_by_name(intents_data, column_name)
    values_lower = [v.lower() for v in values]
    return intents_data[
        grounding_col.astype(str).str.strip().str.lower().isin(values_lower)
    ]


def sample_utterances(
    pool_df: pd.DataFrame, sample_size: int, seed: int, logger: logging.Logger
) -> List[str]:
    """
    Randomly sample `sample_size` utterances from `pool_df`.

    :param pool_df: DataFrame pool of intents to sample from.
    :param sample_size: Number of utterances to sample.
    :param seed: Seed support.
    :param logger: Logger for logging messages.
    :return: List of sampled utterance strings.
    """
    utterance_col = get_column_by_name(pool_df, "Utterance with placeholders")
    total = len(pool_df)
    if sample_size <= 0 or total == 0:
        logger.error(f"No samples: requested={sample_size} available={total}.")
        return []
    if total < sample_size:
        logger.warning(
            f"Requested {sample_size} but only {total} available; reducing sample size."
        )
        sample_size = total
    sampled = pool_df.sample(n=sample_size, random_state=seed)
    results = sampled[utterance_col.name].tolist()
    return results


def sample_utterance_set(
    used_utterances: set,
    count: int,
    seed: int,
    logger: logging.Logger,
    implicit_pool: pd.DataFrame,
    implicit_utterance_col: pd.Series,
    explicit_pool: Optional[pd.DataFrame] = None,
    explicit_utterance_col: Optional[pd.Series] = None,
) -> Dict:
    """
    Sample a set of user utterances from the utterances DataFrame,
    ensuring no duplication with previously used utterances.

    If `explicit_pool` is provided, the function samples half of the utterances from explicit sources
    (grounding values in `explicit_pool`) and the other half from implicit sources (empty grounding).
    If `explicit_pool` is not provided, all sampled utterances are implicit.

    - Implicit utterances are those that do not refer to a specific grounding source,
      meaning they rely on context or general understanding rather than a direct reference.
    - Explicit utterances are those that clearly refer to a specific grounding source,
      providing a direct and identifiable reference.

    :param used_utterances: Set of utterances that have already been used
    :param count: Total number of utterances to sample.
    :param seed: Seed for randomization.
    :param logger: Logger for logging messages.
    :param implicit_pool: DataFrame of utterances with empty grounding.
    :param implicit_utterance_col: Series of their utterance values.
    :param explicit_pool: DataFrame of utterances pre-filtered by explicit grounding (optional).
    :param explicit_utterance_col: Series of their utterance values (optional).
    :return: Dict mapping index to utterances.
    """
    # implicit sampling pool
    implicit_candidates = implicit_pool[~implicit_utterance_col.isin(used_utterances)]

    if explicit_pool is not None and explicit_utterance_col is not None:
        half = count // 2
        # implicit samples
        implicit_samples = sample_utterances(implicit_candidates, half, seed, logger)
        used_utterances.update(implicit_samples)
        # explicit samples
        explicit_candidates = explicit_pool[
            ~explicit_utterance_col.isin(used_utterances)
        ]
        explicit_samples = sample_utterances(explicit_candidates, half, seed, logger)
        used_utterances.update(explicit_samples)
        return {
            "implicit": {i: u for i, u in enumerate(implicit_samples)},
            "explicit": {i: u for i, u in enumerate(explicit_samples)},
        }
    else:
        samples = sample_utterances(implicit_candidates, count, seed, logger)
        used_utterances.update(samples)
        return {i: u for i, u in enumerate(samples)}


def mix_sources_inner(
    meeting_data_path: str,
    intents_data_path: str,
    meeting_data_file_type: str,
    intents_data_file_type: str,
    logger: logging.Logger,
    output_path: str,
    num_of_intents_per_transcript: int = 3,
    num_of_intents_per_chat: int = 2,
    num_of_intents_per_vi: int = 2,
    seed: int = 42,
    meeting_data_filter_funcs: List[str] = [],
    intents_data_filter_funcs: List[str] = [],
    output_file_name: str = "merged_sources.json",
):
    """
    Mix the meeting data (meeting file) with the intents data and write the output to the specified path:
      1. Applies filtering functions to the meeting data.
      2. Filters meetings into those that require transcript and/or chat queries.
      3. Uses dedicated functions to perform random sampling for transcript and chat queries.
         For chat queries, the result is returned as a dict with two lists: one for explicit and one for implicit intents.

    Parameters:
    :param meeting_data_path: Path to the meeting data file.
    :param intents_data_path: Path to the intents data file.
    :param meeting_data_file_type: File type of the meeting data.
    :param intents_data_file_type: File type of the intents' data.
    :param logger: Logger for logging messages.
    :param output_path: Output path where the merged file will be written.
    :param num_of_intents_per_transcript: Number of transcript intents to sample per meeting. Defaults to 3.
    :param num_of_intents_per_chat: Total number of chat intents to sample per meeting (must be even). Defaults to 2.
    :param num_of_intents_per_vi: Total number of VI intents to sample per meeting (must be even). Defaults to 2.
    :param seed: Seed for randomization. Defaults to 42.
    :param meeting_data_filter_funcs: List of filtering function names to apply to the meeting data.
    :param intents_data_filter_funcs: List of filtering function names to apply to the intents' data.
    :param output_file_name: Name of the output file. Defaults to "merged_sources.json".
    """
    allow_functions: Dict[str, Any] = {
        "filter_on_intents": filter_on_intents,
        "filter_on_context_dependent": filter_on_context_dependant,
    }

    meeting_data_iterator = read_all_files_of_json_iterator(
        meeting_data_path, meeting_data_file_type, logger
    )
    intents_data_df = read_all_files_of_type(
        intents_data_path, intents_data_file_type, logger
    )

    # Apply intents data filtering once outside the generator
    if intents_data_filter_funcs:
        for func_name in intents_data_filter_funcs:
            if func_name in allow_functions:
                len_before = len(intents_data_df)
                logger.info(f"Applying intents data filter function: {func_name}")
                intents_data_df = allow_functions[func_name](intents_data_df)
                logger.info(f"Filtered {len_before - len(intents_data_df)} rows.")
            else:
                logger.error(f"Invalid intents data filter function: {func_name}")
        logger.info(
            f"{len(intents_data_df)} rows remain in intents data after filtering."
        )

    if intents_data_df.empty:
        logger.error("No data left in intents data after filtering.")
        raise ValueError("No data left in intents data after filtering.")

    # precompute implicit pool once outside the generator
    implicit_pool = filter_on_empty_grounding(intents_data_df)
    implicit_utterance_col = get_column_by_name(
        implicit_pool, "Utterance with placeholders"
    )
    # precompute explicit pools once outside the generator
    explicit_pool_chat = filter_by_grounding_values(intents_data_df, ["chat", "links"])
    explicit_utterance_col_chat = get_column_by_name(
        explicit_pool_chat, "Utterance with placeholders"
    )
    explicit_pool_vi = filter_by_grounding_values(intents_data_df, ["VI"])
    explicit_utterance_col_vi = get_column_by_name(
        explicit_pool_vi, "Utterance with placeholders"
    )

    def process_meeting_data_generator():
        """Generator function to process meeting data in chunks and yield processed data as dicts"""
        any_data_processed = False

        for meeting_data_df in meeting_data_iterator:
            logger.info(f"meeting_data_df shape: [{meeting_data_df.shape}]")
            logger.info(f"intents_data_df shape: [{intents_data_df.shape}]")
            if meeting_data_df is None:
                logger.error("Error loading meeting data.")
                raise ValueError("Failed to load meeting data.")

            if meeting_data_filter_funcs:
                for func_name in meeting_data_filter_funcs:
                    if func_name in allow_functions:
                        len_before = len(meeting_data_df)
                        logger.info(
                            f"Applying meeting data filter function: {func_name}"
                        )
                        meeting_data_df = allow_functions[func_name](meeting_data_df)
                        logger.info(
                            f"Filtered {len_before - len(meeting_data_df)} rows."
                        )
                    else:
                        logger.error(
                            f"Invalid meeting data filter function: {func_name}"
                        )
                logger.info(
                    f"{len(meeting_data_df)} rows remain in meeting data after filtering."
                )

            if meeting_data_df.empty:
                logger.warning(
                    "No data left in meeting data chunk after filtering, skipping chunk."
                )
                continue

            logger.info(
                f"Beginning per-meeting sampling for {len(meeting_data_df)} meetings"
            )

            transcript_queries_examples = {}
            chat_queries_examples = {}
            vi_queries_examples = {}

            for meeting_id in meeting_data_df.index:
                used: set = set()

                if "full_transcript" in meeting_data_df.columns:
                    transcript_queries_examples[meeting_id] = sample_utterance_set(
                        used,
                        num_of_intents_per_transcript,
                        seed,
                        logger,
                        implicit_pool,
                        implicit_utterance_col,
                    )
                else:
                    logger.info(
                        f"Meeting {meeting_id} is missing the 'full_transcript' field. Skipping.."
                    )

                if "final_chat_messages" in meeting_data_df.columns:
                    chat_queries_examples[meeting_id] = sample_utterance_set(
                        used,
                        num_of_intents_per_chat,
                        seed,
                        logger,
                        implicit_pool,
                        implicit_utterance_col,
                        explicit_pool_chat,
                        explicit_utterance_col_chat,
                    )
                else:
                    logger.info(
                        f"Meeting {meeting_id} is missing the 'final_chat_messages' field. Skipping.."
                    )

                if "generated_vi" in meeting_data_df.columns:
                    vi_queries_examples[meeting_id] = sample_utterance_set(
                        used,
                        num_of_intents_per_vi,
                        seed,
                        logger,
                        implicit_pool,
                        implicit_utterance_col,
                        explicit_pool_vi,
                        explicit_utterance_col_vi,
                    )
                else:
                    logger.info(
                        f"Meeting {meeting_id} is missing the 'generated_vi' field. Skipping.."
                    )

            if len(transcript_queries_examples) > 0:
                meeting_data_df["transcript_queries_examples"] = (
                    meeting_data_df.index.map(transcript_queries_examples)
                )
            if len(chat_queries_examples) > 0:
                meeting_data_df["chat_queries_examples"] = meeting_data_df.index.map(
                    chat_queries_examples
                )
            if len(vi_queries_examples) > 0:
                meeting_data_df["vi_queries_examples"] = meeting_data_df.index.map(
                    vi_queries_examples
                )

            any_data_processed = True
            # Convert DataFrame to dict and yield
            yield meeting_data_df.to_dict(orient="index")

        if not any_data_processed:
            logger.error("No data left in meeting data after filtering.")
            raise ValueError("No data left in meeting data after filtering.")

    # Use streaming write to avoid OOM issues
    write_file_to_storage_with_streaming(
        output_path=output_path,
        filename=output_file_name,
        logger=logger,
        data_iterator=process_meeting_data_generator(),
    )
