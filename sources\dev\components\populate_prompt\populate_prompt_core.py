"""
This module provides the core functionality for injecting grounding information into prompts.
"""

import copy
import enum
import json
import logging
import re
from dataclasses import dataclass
from typing import Dict, List, Callable, Tuple
import random
import pandas as pd
import itertools

from tools.io_helpers import (
    read_all_files_of_type,
    read_all_files_of_json_iterator,
    read_file_in_path,
    write_file_to_storage_with_streaming,
    make_json_serializable,
)
from tools.dict_helpers import delete_nested_item, recursive_get_item
from tools.llm_utilities import OutputPromptFormatType, output_generated_prompts


@dataclass
class ComponentOutputsInner:
    messages_in_prompt: int


def process_all_participants(
    df: pd.DataFrame,
    logger: logging.Logger,
    all_participants_expressions=["all participants", "all"],
) -> pd.DataFrame:
    """
    Process "all participants" speakers value in the dataframe for meeting_minutes items, and replace it with the actual participants
    :param df: the dataframe
    :param logger: the logger
    :param all_participants_expressions: the expressions to identify "all participants" in the speakers value
    :return: the processed dataframe
    """
    all_participants_expressions = [
        expression.lower() for expression in all_participants_expressions
    ]
    logger.info(
        f"Processing all participants with all_participants_expressions: {all_participants_expressions}"
    )
    delete_index = []
    if "meeting_minutes" in df.columns:
        for index, row in df.iterrows():
            for minutes in row["meeting_minutes"]["meeting_minutes"].values():
                if "speakers" in minutes:
                    if any(
                        all_participants_expression
                        in [
                            speaker.strip()
                            for speaker in minutes["speakers"].lower().split(",")
                        ]
                        for all_participants_expression in all_participants_expressions
                    ):
                        if "meeting_params" in df.columns:
                            if "participants" in df.loc[index, "meeting_params"]:
                                minutes["speakers"] = df.loc[index, "meeting_params"][
                                    "participants"
                                ]
                                logger.info(
                                    f"process_all_participants: Replaced 'all participants' with the actual participants for index {index}"
                                )
                            else:
                                logger.error(
                                    f"process_all_participants: Participants not found in the meeting_params for index {index}, deleting the row",
                                )
                                delete_index.append(index)
                        else:
                            logger.error(
                                f"process_all_participants: meeting_params not found in the dataframe, \
                                                    cannot replace 'all participants' with the actual participants for index {index}, deleting the row",
                            )
                            delete_index.append(index)
                else:
                    logger.warning(
                        f"process_all_participants: speakers not found in the meeting_minutes for index {index}, skipping for this index",
                    )
    else:
        logger.error(
            "process_all_participants: meeting_minutes not found in the dataframe, cannot replace 'all participants' with the actual participants, skipping the process",
        )
    df = df.drop(delete_index)
    return df


def filter_df_for_ft(df: pd.DataFrame, logger: logging.Logger) -> pd.DataFrame:
    """
    Filter the dataframe based on transcripts duplication and no answer responses
    :param df: the dataframe
    :param logger: the logger
    :return: the filtered dataframe
    """
    responses_to_filter = [
        "I'm sorry, but I couldn't find the information you requested in the meeting transcript.",
        "Error in model_answer_prediction",
    ]
    logger.info("Filtering the dataframe")
    if "transcript" not in df.columns:
        logger.error(
            "The column 'transcript' is not found in the dataframe, cannot filter based on its value",
        )
    else:
        df = df.drop_duplicates(
            subset=["transcript"]
        )  # remove duplicates in transcripts
        logger.info(
            f"The length of the dataset after removing input duplications: {len(df)}",
        )
    if "completion" not in df.columns:
        logger.error(
            "The column 'completion' is not found in the dataframe, cannot filter based on its value",
        )
        return df
    for filter_response in responses_to_filter:
        df = df[
            ~df["completion"].str.contains(filter_response)
        ]  # remove rows with no response
        logger.info(
            f"The length of the dataset after removing the response: '{filter_response}'  :    {len(df)}",
        )
    return df


@dataclass
class FilterType(enum.Enum):
    """An enum to the filter type of the data"""

    filter_for_ft = filter_df_for_ft
    process_all_participants = process_all_participants


def enum_filtering_func(enum_value):
    try:
        filter_func = getattr(FilterType, enum_value)
    except AttributeError:
        filter_func = None
    return filter_func


def fill_prompt(
    prompt_template: Dict, placeholders: List[str], data: Dict, logger: logging.Logger
) -> Dict:
    """
    Fill the prompt with the placeholders from the data
    """
    for placeholder in placeholders:
        if placeholder in data:
            placeholder_replaced_count = 0
            for message in prompt_template[
                "messages"
            ]:  # replace the placeholder in all messages
                if (
                    f"{{{{{placeholder}}}}}" in message["content"]
                ):  # generally, we do not need this check since replace does not raise an error if the placeholder is not found (i.e. no replacement), but we used it for a counter check
                    placeholder_replaced_count += 1
                    # Use make_json_serializable to preserve data types when converting to string
                    serialized_value = make_json_serializable(data[placeholder])
                    if isinstance(serialized_value, (int, float)):
                        # For numeric values, convert to JSON representation to preserve type
                        placeholder_value = json.dumps(serialized_value)
                    else:
                        # For other types, convert to string
                        placeholder_value = str(serialized_value)

                    message["content"] = message["content"].replace(
                        f"{{{{{placeholder}}}}}", placeholder_value
                    )
            logger.info(
                f"Placeholder {placeholder} replaced in {placeholder_replaced_count} messages",
            )
        else:
            logger.error(
                f"fill_prompt: Placeholder {placeholder} not found in the data",
            )
    return prompt_template


def collect_jsonl_generator(
    input_path: str,
    logger: logging.Logger,
    filter_func: Callable = None,
    chunk_size: int = 1000,
):
    """
    Collect the jsonl files into an iterator after filtering to avoid OOM issues
    :param input_path: the input path for the jsonl files containing the transcript and completion
    :param logger: the logger
    :param filter_func: the filtering function to filter the data. Default is None
    :param chunk_size: the chunk size for reading JSON files. Default is 10
    :return: iterator yielding filtered data chunks
    """
    # Try JSONL first
    pairs_df = read_all_files_of_type(input_path, "jsonl", logger)
    if pairs_df is not None:
        logger.info(f"number of rows in jsonl: {len(pairs_df)}")
        if filter_func:
            filter_func(pairs_df, logger)
        if "request_metadata" not in pairs_df.columns:
            pairs_df.index.name = "request_metadata"
            pairs_df = pairs_df.reset_index()
        # Yield the entire JSONL data as one chunk (assuming JSONL files are already manageable)
        chunk_records = pairs_df.to_dict(orient="records")
        # Apply make_json_serializable to preserve proper data types
        chunk_records = [make_json_serializable(record) for record in chunk_records]
        yield chunk_records
    else:
        logger.error("No jsonl files found or error reading files")
        # Use iterator approach for JSON files to avoid OOM issues
        logger.info(
            f"Trying to read JSON files with iterator approach to avoid OOM, chunk_size={chunk_size}"
        )
        any_data_found = False

        for df_chunk in read_all_files_of_json_iterator(
            input_path, "json", logger, chunk_size=chunk_size
        ):
            any_data_found = True
            logger.info(f"Processing chunk with {len(df_chunk)} rows")

            if filter_func:
                filter_func(df_chunk, logger)

            if "request_metadata" not in df_chunk.columns:
                df_chunk.index.name = "request_metadata"
                df_chunk = df_chunk.reset_index()

            chunk_records = df_chunk.to_dict(orient="records")
            if chunk_records:  # Only yield if there are records
                # Apply make_json_serializable to preserve proper data types
                chunk_records = [
                    make_json_serializable(record) for record in chunk_records
                ]
                yield chunk_records

        if not any_data_found:
            logger.error("No json files found or error reading files")
            return


def collect_jsonl(
    input_path: str,
    logger: logging.Logger,
    filter_func: Callable = None,
    chunk_size: int = 1000,
):
    """
    Collect the jsonl files and return as a list for backward compatibility with tests
    :param input_path: the input path for the jsonl files containing the transcript and completion
    :param logger: the logger
    :param filter_func: the filtering function to filter the data. Default is None
    :param chunk_size: the chunk size for reading JSON files. Default is 10
    :return: list of filtered data records
    """
    result = []
    for chunk in collect_jsonl_generator(input_path, logger, filter_func, chunk_size):
        result.extend(chunk)
    return result


def allocate_utterances_by_importance(
    agenda_item: Dict,
    total_utterances: int,
    is_generate_events: bool = False,
    seed: int = None,
) -> None:
    """
    Allocate the utterances based on the importance of the agenda item
    :param agenda_item: the agenda item to allocate the utterances for
    :param total_utterances: the total number of utterances
    :param is_generate_events: whether to generate events for the agenda item
    :param seed: the seed for randomization
    """
    if seed is not None:
        random.seed(seed)
    else:
        random.seed(42)

    if "importance" in agenda_item:
        agenda_item["number of utterances"] = int(
            float(agenda_item["importance"]) * total_utterances
        )
        del agenda_item["importance"]
        if is_generate_events:
            agenda_item["number of events"] = max(
                1, int(agenda_item["number of utterances"] / random.randint(7, 12))
            )


def preprocess_placeholders_content(item: Dict) -> Dict:
    """
    Preprocess the placeholders content
    :param item: the item to preprocess
    :return: the preprocessed item
    """
    # for preprocess_func in item.preprocess_funcs:
    #     preprocess_func(item)
    convert_proportion_to_count(
        item
    )  # currently we have only a single preprocess function


def convert_proportion_to_count(item: Dict) -> Dict:
    """
    Convert the proportion of the agenda items to the number of utterances
    :param item: the item to preprocess
    :return: the preprocessed item
    """
    if (
        (
            (
                "agenda" in item
                and "agenda" in item["agenda"]
                and isinstance(item["agenda"]["agenda"], dict)
            )
            or "agenda.agenda.item" in item
        )
        and "transcript_args" in item
        and "number_of_utterances" in item["transcript_args"]
    ):
        total_utterances = item["transcript_args"]["number_of_utterances"]
        if "agenda.agenda.item" in item:
            allocate_utterances_by_importance(
                item["agenda.agenda.item"], total_utterances, is_generate_events=True
            )
        else:
            agenda_response = item["agenda"]
            for agenda_item in agenda_response["agenda"].values():
                allocate_utterances_by_importance(
                    agenda_item, total_utterances, is_generate_events=False
                )


def get_placeholders(
    prompt_template: Dict, pattern: str = r"\{\{(.*?)\}\}"
) -> List[str]:
    """
    Get the placeholders from the prompt template
    :param prompt_template: the prompt template is a dictionary containing list of messages with placeholders. i.e. prompt_template["messages"][0] is the prompt message as a dict of "role" and "content"
    :param pattern: the pattern to search for the placeholders. Default is {{placeholder}}
    :return: a list of placeholders names
    """
    placeholders = []
    contents = [message["content"] for message in prompt_template["messages"]]
    pattern = re.compile(pattern)
    for content in contents:
        matches = pattern.findall(content)
        placeholders.extend(matches)
    return placeholders


def get_message_count(prompt_template):
    return len(prompt_template["messages"])


def get_prompt(
    prompt_name: str, prompts_path: str, logger: logging.Logger
) -> Tuple[Dict, List[str], int]:
    """
    Get the prompt template
    :param prompt_name: the name of the prompt
    :param prompts_path: the path to the prompts files (json)
    :param logger: the logger
    :return: the prompt template as a dictionary, a list of placeholders names, and the number of messages in the prompt
    """
    prompt_template = read_file_in_path(prompts_path, f"{prompt_name}.json", logger)
    if prompt_template is None:
        raise ValueError(f"Prompt name {prompt_name} not found")
    placeholders = get_placeholders(prompt_template)
    message_count = get_message_count(prompt_template)
    return prompt_template, placeholders, message_count


def split_list_of_items(
    placeholder: str,
    placeholders_content: List[Dict],
    placeholder_split: List[str],
    logger: logging.Logger,
) -> List[Dict]:
    """
    Split the placeholder content to multiple items based on the placeholder_split location. placeholder_split is a key in the dictionary that its value is a list of dictionaries
    :param placeholder:  a string of the placeholder in the prompt in the format of "key1.key2.key3"
    :param placeholders_content: the placeholders content to extract the items from
    :param placeholder_split: the list of keys in the dictionary that its value is a list of dictionaries to split on
    :param logger: the logger
    :return: the splitted placeholder content
    """
    new_placeholders_content = []
    for item in placeholders_content:
        try:
            placeholder_item = recursive_get_item(item, placeholder_split, logger)
        except TypeError as e:
            raise ValueError(
                f"split_list_of_items with {item.get('request_metadata', 'request_metadata')}"
                + str(e)
            )
        if isinstance(placeholder_item, dict):
            for i, placeholder_item_dict in placeholder_item.items():
                new_item = copy.deepcopy(item)
                new_item[placeholder] = placeholder_item_dict
                if "request_metadata" in new_item:
                    new_item["request_metadata"] = (
                        f"{new_item['request_metadata']}_{i}"  # Create a unique request_metadata for each item, but keep the original request_metadata for later merging
                    )
                new_placeholders_content.append(new_item)
        else:
            logger.error(
                f"split_list_of_items: Placeholder {placeholder} is not a dictionary in item {item}"
            )
            return placeholders_content
    return new_placeholders_content


def split_placeholder_batch(
    placeholder: str, placeholders_content: List[Dict], logger: logging.Logger
) -> List[Dict]:
    """
    Split the placeholder content (i.e. item) based on the dot
    :param placeholder: the placeholder
    :param placeholders_content: the sample: list of placeholders content.
    :param logger: the logger
    :return: the split placeholder content
    """

    placeholder_split = placeholder.split(".")
    if (
        placeholder_split[-1] == "item"
    ):  #'item is a special keyword to split a list of dictionaries in placeholders_content[:-1] level
        new_placeholders_content = split_list_of_items(
            placeholder, placeholders_content, placeholder_split[:-1], logger
        )
        for placeholder_content in new_placeholders_content:
            delete_nested_item(
                placeholder_content, placeholder_split[:-1]
            )  # delete the path to the list of dictionaries from each item
        return new_placeholders_content
    else:
        for placeholder_content in placeholders_content:
            placeholder_content[placeholder] = recursive_get_item(
                placeholder_content, placeholder_split, logger
            )
        return placeholders_content


def split_placeholder(
    placeholder: str, placeholders_content: Dict, logger: logging.Logger
) -> List[Dict]:
    """
    Split the placeholder content (i.e. item) based on the dot
    :param placeholder: the placeholder
    :param placeholders_content: the placeholders content for a single sample
    :param logger: the logger
    :return: the split placeholder content
    """
    if not placeholders_content:
        logger.error(
            f"split_placeholder: Placeholder content is empty for {placeholder}"
        )
        return []
    placeholder_split = placeholder.split(".")
    if (
        placeholder_split[-1] == "item"
    ):  #'item is a special keyword to split a list of dictionaries in placeholders_content[:-1] level
        new_placeholders_content = split_list_of_items(
            placeholder, [placeholders_content], placeholder_split[:-1], logger
        )
        for placeholder_content in new_placeholders_content:
            delete_nested_item(
                placeholder_content, placeholder_split[:-1]
            )  # delete the path to the list of dictionaries from each item
        return new_placeholders_content
    else:
        for placeholder_content in placeholders_content:
            placeholder_content[placeholder] = recursive_get_item(
                placeholder_content, placeholder_split, logger
            )
        return placeholders_content


def read_question_id_prompt_name_mapper(
    prompts_path: str, logger: logging.Logger
) -> Dict:
    """
    Read the question_id_prompt_name_mapper.json file
    :param prompts_path: the path to the prompts files (json) and the question_id_prompt_name_mapper.json file
    :param logger: the logger
    :return: the question_id_prompt_mapper as a dictionary
    """
    question_id_prompt_name_mapper = read_file_in_path(
        prompts_path, "question_id_prompt_name_mapper.json", logger
    )
    if question_id_prompt_name_mapper is None:
        logger.error("question_id_prompt_name_mapper.json not found")
        raise ValueError("question_id_prompt_name_mapper.json not found")
    if not isinstance(question_id_prompt_name_mapper, dict):
        logger.error("question_id_prompt_name_mapper.json is not a dictionary")
        raise ValueError("question_id_prompt_name_mapper.json is not a dictionary")

    question_id_prompt_mapper = {}
    for question_id, prompt_name in question_id_prompt_name_mapper.items():
        prompt_template, placeholders, message_count = get_prompt(
            prompt_name, prompts_path, logger
        )
        question_id_prompt_mapper[question_id] = {
            "prompt_template": prompt_template,
            "placeholders": placeholders,
            "message_count": message_count,
        }
        # Load chats prompt
        prompt_template, placeholders, message_count = get_prompt(
            prompt_name + "_with_chat", prompts_path, logger
        )
        question_id_prompt_mapper[question_id + "_with_chat"] = {
            "prompt_template": prompt_template,
            "placeholders": placeholders,
            "message_count": message_count,
        }
    return question_id_prompt_mapper


def process_placeholders_data_generator(
    placeholders_content_iterator,
    prompt_name: str,
    prompts_path: str,
    evaluate_mode: bool,
    do_shuffle: bool,
    seed: int,
    logger: logging.Logger,
):
    """
    Generator function to process placeholders data and yield processed items with completion info.
    This function processes data in streaming fashion to avoid OOM issues.
    """
    total_rows_processed = 0

    if prompt_name:  # fill the same prompt for all items
        logger.info(f"Requested prompt for injection: {prompt_name}")
        prompt_template, placeholders, message_count = get_prompt(
            prompt_name, prompts_path, logger
        )
        if prompt_template is None:
            logger.error(f"Prompt name {prompt_name} not found")
            raise ValueError(f"Prompt name {prompt_name} not found")

        # If shuffle is needed, collect all data first
        if do_shuffle:
            logger.warning(
                "Shuffle mode enabled - collecting all data in memory for shuffling. This may cause OOM for large datasets."
            )
            all_items = []

            for placeholders_content_chunk in placeholders_content_iterator:
                if not placeholders_content_chunk:
                    continue

                chunk_size = len(placeholders_content_chunk)
                total_rows_processed += chunk_size
                logger.info(
                    f"Processing chunk with {chunk_size} rows, total processed: {total_rows_processed}"
                )

                chunk_placeholders_content, chunk_completions = (
                    fill_prompt_with_placeholders_for_all_samples(
                        placeholders_content_chunk,
                        prompt_template,
                        placeholders,
                        evaluate_mode,
                        logger,
                    )
                )

                # Store items for shuffling
                if isinstance(chunk_placeholders_content, list):
                    for i, item in enumerate(chunk_placeholders_content):
                        all_items.append(
                            {
                                "placeholders_content": item,
                                "completion": chunk_completions[i],
                            }
                        )
                else:
                    for completion_item in chunk_completions:
                        all_items.append(
                            {
                                "placeholders_content": chunk_placeholders_content,
                                "completion": completion_item,
                            }
                        )

            # Shuffle all items
            random.seed(seed)
            random.shuffle(all_items)

            # Yield shuffled items
            for item in all_items:
                yield item
        else:
            # Stream processing without shuffle - memory efficient
            for placeholders_content_chunk in placeholders_content_iterator:
                if not placeholders_content_chunk:
                    continue

                chunk_size = len(placeholders_content_chunk)
                total_rows_processed += chunk_size
                logger.info(
                    f"Processing chunk with {chunk_size} rows, total processed: {total_rows_processed}"
                )

                chunk_placeholders_content, chunk_completions = (
                    fill_prompt_with_placeholders_for_all_samples(
                        placeholders_content_chunk,
                        prompt_template,
                        placeholders,
                        evaluate_mode,
                        logger,
                    )
                )

                # Yield items immediately
                if isinstance(chunk_placeholders_content, list):
                    for i, item in enumerate(chunk_placeholders_content):
                        yield {
                            "placeholders_content": item,
                            "completion": chunk_completions[i],
                        }
                else:
                    for completion_item in chunk_completions:
                        yield {
                            "placeholders_content": chunk_placeholders_content,
                            "completion": completion_item,
                        }

    else:  # fill the prompt for each item separately
        logger.info("Filling the prompt for each item separately")
        question_id_prompt_mapper = read_question_id_prompt_name_mapper(
            prompts_path, logger
        )
        if question_id_prompt_mapper is None:
            logger.error("question_id_prompt_mapper.json not found")
            raise ValueError("question_id_prompt_mapper.json not found")

        # If shuffle is needed, collect all data first
        if do_shuffle:
            logger.warning(
                "Shuffle mode enabled - collecting all data in memory for shuffling. This may cause OOM for large datasets."
            )
            all_items = []

            for placeholders_content_chunk in placeholders_content_iterator:
                if not placeholders_content_chunk:
                    continue

                chunk_size = len(placeholders_content_chunk)
                total_rows_processed += chunk_size
                logger.info(
                    f"Processing chunk with {chunk_size} rows, total processed: {total_rows_processed}"
                )

                # Process each record individually
                for placeholders_content_single_record in placeholders_content_chunk:
                    if "question_id" in placeholders_content_single_record:
                        if "final_chat_messages" in placeholders_content_single_record:
                            question_id = (
                                placeholders_content_single_record["question_id"]
                                + "_with_chat"
                            )
                        else:
                            question_id = placeholders_content_single_record[
                                "question_id"
                            ]

                        prompt = question_id_prompt_mapper.get(question_id, None)
                        if prompt is None:
                            logger.error(
                                f"Prompt name for question_id {placeholders_content_single_record['question_id']} not found. skipping the item {placeholders_content_single_record['request_metadata']}"
                            )
                            continue

                        processed_record, completion = (
                            fill_prompt_with_placeholders_for_all_samples(
                                placeholders_content_single_record,
                                prompt["prompt_template"],
                                prompt["placeholders"],
                                evaluate_mode,
                                logger,
                            )
                        )
                        if completion is None:
                            continue

                        # Store items for shuffling
                        for completion_item in completion:
                            all_items.append(
                                {
                                    "placeholders_content": processed_record,
                                    "completion": completion_item,
                                }
                            )

            # Shuffle all items
            random.seed(seed)
            random.shuffle(all_items)

            # Yield shuffled items
            for item in all_items:
                yield item
        else:
            # Stream processing without shuffle - memory efficient
            for placeholders_content_chunk in placeholders_content_iterator:
                if not placeholders_content_chunk:
                    continue

                chunk_size = len(placeholders_content_chunk)
                total_rows_processed += chunk_size
                logger.info(
                    f"Processing chunk with {chunk_size} rows, total processed: {total_rows_processed}"
                )

                # Process each record individually
                for placeholders_content_single_record in placeholders_content_chunk:
                    if "question_id" in placeholders_content_single_record:
                        if "final_chat_messages" in placeholders_content_single_record:
                            question_id = (
                                placeholders_content_single_record["question_id"]
                                + "_with_chat"
                            )
                        else:
                            question_id = placeholders_content_single_record[
                                "question_id"
                            ]

                        prompt = question_id_prompt_mapper.get(question_id, None)
                        if prompt is None:
                            logger.error(
                                f"Prompt name for question_id {placeholders_content_single_record['question_id']} not found. skipping the item {placeholders_content_single_record['request_metadata']}"
                            )
                            continue

                        processed_record, completion = (
                            fill_prompt_with_placeholders_for_all_samples(
                                placeholders_content_single_record,
                                prompt["prompt_template"],
                                prompt["placeholders"],
                                evaluate_mode,
                                logger,
                            )
                        )
                        if completion is None:
                            continue

                        # Yield items immediately
                        for completion_item in completion:
                            yield {
                                "placeholders_content": processed_record,
                                "completion": completion_item,
                            }


def inject_from_jsonl_into_prompt(
    input_data: str,
    prompt_name: str,
    prompts_path: str,
    output_path: str,
    completion_path: str,
    max_tokens: int,
    temperature: float,
    output_prompt_format: OutputPromptFormatType,
    filter_items: str,
    evaluate_mode: bool,
    do_shuffle: bool,
    seed: int,
    logger: logging.Logger,
    chunk_size: int = 1000,
):
    """
    Inject grounding information into the prompt
    :param input_data: the input path to the jsonl file containing the transcript and completion
    :param prompt_name: the name of the prompt to inject the grounding information into. Prompt should contain {{transcript}} and {{completion}}. If None, prompt is selected by "question_id" in the input_data.
    :param prompts_path: the path to the prompts files (json)
    :param output_path: the output path of the info file containing the transcript and completion
    :param completion_path: the output path of the completions file fitting the fine-tuning component
    :param max_tokens: the max tokens to use in the generation
    :param temperature: the temperature to use in the generation
    :param output_prompt_format: the format of the output prompt
    :param filter_items: a string representing the filter function to use (FilterType)
    :param evaluate_mode: evaluate_mode
    :param do_shuffle: whether to shuffle the populated prompts
    :param seed: the seed to use for shuffling the populated prompts
    :param logger: the logger
    """
    logger.info(
        f"collect_jsonl with filter_items:{filter_items}, chunk_size:{chunk_size}"
    )
    placeholders_content_iterator = collect_jsonl_generator(
        input_data,
        logger,
        filter_func=enum_filtering_func(filter_items),
        chunk_size=chunk_size,
    )

    # Simple message_count logic like original code
    if prompt_name:
        _, _, message_count = get_prompt(prompt_name, prompts_path, logger)
    else:
        message_count = None  # Multiple prompts, cannot count messages

    # Use generator to process data and yield individual items directly
    if do_shuffle:
        logger.info(
            "Processing data with shuffle mode - will collect all data in memory"
        )
    else:
        logger.info("Processing data with streaming approach - memory efficient")

    processed_items_generator = process_placeholders_data_generator(
        placeholders_content_iterator,
        prompt_name,
        prompts_path,
        evaluate_mode,
        do_shuffle,
        seed,
        logger,
    )

    # Generator for placeholders_content to write
    def placeholders_generator():
        for item in processed_items_generator:
            yield item["placeholders_content"]

    # Generator for completions to write
    def completions_generator():
        for item in processed_items_generator:
            yield item["completion"]

    # Since we need to use the generator twice, we need to handle this carefully
    # If shuffle is enabled, we already collected all data in memory in process_placeholders_data_generator
    # If shuffle is disabled, we need to use itertools.tee to split the generator

    if do_shuffle:
        # For shuffle mode, we can safely split the generator since data is already in memory
        processed_items_gen1, processed_items_gen2 = itertools.tee(
            processed_items_generator, 2
        )

        def placeholders_generator_shuffled():
            for item in processed_items_gen1:
                yield item["placeholders_content"]

        # Use streaming write directly - write placeholders_content items as JSONL
        write_file_to_storage_with_streaming(
            output_path,
            f"injected_{prompt_name}_placeholders.jsonl",
            logger,
            placeholders_generator_shuffled(),
        )

        # Extract completions from the second iterator
        completions = []
        any_data_found = False
        for item in processed_items_gen2:
            completions.append(item["completion"])
            any_data_found = True

    else:
        # For streaming mode, process once and collect both placeholders and completions
        logger.info(
            "Streaming mode: processing data once to collect both placeholders and completions"
        )
        placeholders_content_iterator = collect_jsonl_generator(
            input_data,
            logger,
            filter_func=enum_filtering_func(filter_items),
            chunk_size=chunk_size,
        )
        processed_items_generator = process_placeholders_data_generator(
            placeholders_content_iterator,
            prompt_name,
            prompts_path,
            evaluate_mode,
            False,  # No shuffle for streaming
            seed,
            logger,
        )

        # Collect both placeholders_content and completions in one pass
        placeholders_list = []
        completions = []
        any_data_found = False

        for item in processed_items_generator:
            placeholders_list.append(item["placeholders_content"])
            completions.append(item["completion"])
            any_data_found = True

        # Write placeholders_content to file
        if placeholders_list:

            def placeholders_generator_streaming():
                for item in placeholders_list:
                    yield item

            write_file_to_storage_with_streaming(
                output_path,
                f"injected_{prompt_name}_placeholders.jsonl",
                logger,
                placeholders_generator_streaming(),
            )

    if not any_data_found:
        logger.info("No placeholders_content found after filtering")
        return ComponentOutputsInner(messages_in_prompt=message_count)

    logger.info(f"Total completions collected: {len(completions)}")
    output_generated_prompts(
        logger,
        completion_path,
        output_prompt_format,
        completions,
        max_tokens,
        temperature,
    )

    return ComponentOutputsInner(messages_in_prompt=message_count)


def fill_prompt_with_placeholders_for_all_samples(
    placeholders_content, prompt_template, placeholders, evaluate_mode, logger
):
    """ "
    Fill the prompt with the placeholders from the data"
    :param placeholders_content: the placeholders content to fill the prompt with. Either a list of dictionaries or a single dictionary
    :param prompt_template: the prompt template to fill the placeholders in
    :param placeholders: the placeholders to fill in the prompt
    :param evaluate_mode: whether to use the evaluate mode
    :param logger: the logger
    :return: the filled prompt
    """
    completions = []
    for placeholder in placeholders:
        if "." in placeholder:
            # split the placeholder content (i.e. item) based on the dot
            if isinstance(placeholders_content, dict):
                placeholders_content = split_placeholder(
                    placeholder, placeholders_content, logger
                )
            else:
                placeholders_content = split_placeholder_batch(
                    placeholder, placeholders_content, logger
                )
    if isinstance(placeholders_content, dict):
        populate_prompt_with_placeholders(
            prompt_template,
            placeholders,
            evaluate_mode,
            logger,
            completions,
            placeholders_content,
        )
        return placeholders_content, completions
    for placeholders_content_single_sample in placeholders_content:
        populate_prompt_with_placeholders(
            prompt_template,
            placeholders,
            evaluate_mode,
            logger,
            completions,
            placeholders_content_single_sample,
        )
    return placeholders_content, completions


def populate_prompt_with_placeholders(
    prompt_template,
    placeholders,
    evaluate_mode,
    logger,
    completions,
    placeholders_content_single_sample,
):
    """
    Populate the prompt with the placeholders from the data
    :param prompt_template: the prompt template to fill the placeholders in
    :param placeholders: the placeholders to fill in the prompt
    :param evaluate_mode: whether to use the evaluate mode
    :param logger: the logger
    :param completions: the list of completions to fill the prompt with
    :param placeholders_content_single_sample: the placeholders content to fill the prompt with. Either a list of dictionaries or a single dictionary
    """
    if evaluate_mode:
        # Apply make_json_serializable to preserve data types before JSON serialization
        serializable_sample = make_json_serializable(placeholders_content_single_sample)
        item_json_str = json.dumps(serializable_sample)
        item_dict = {"formatted_transcript": item_json_str}
        placeholders_content_single_sample["prompt"] = fill_prompt(
            copy.deepcopy(prompt_template), placeholders, item_dict, logger
        )
        placeholders_content_single_sample["prompt"]["request_metadata"] = "example"
        completions.append(placeholders_content_single_sample["prompt"])
        logger.info("Filled the prompt for item")
    else:
        preprocess_placeholders_content(placeholders_content_single_sample)
        placeholders_content_single_sample["prompt"] = fill_prompt(
            copy.deepcopy(prompt_template),
            placeholders,
            placeholders_content_single_sample,
            logger,
        )
        if "request_metadata" in placeholders_content_single_sample:
            placeholders_content_single_sample["prompt"]["request_metadata"] = str(
                placeholders_content_single_sample["request_metadata"]
            )  # ensure the request_metadata is a string
            logger.info(
                f"Filled the prompt for: {placeholders_content_single_sample['request_metadata']}"
            )
        completions.append(placeholders_content_single_sample["prompt"])
