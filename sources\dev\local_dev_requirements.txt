pylint==2.13.9
pylint_junit==0.3.2
pytest==6.2.5
numpy==1.26.4
pandas==2.2.2
importlib-resources==5.9.0  # https://github.com/python/importlib_resources/issues/80
dask[dataframe]==2024.8.0
SoundsLike==0.0.11
black==24.10.0
pytest-cov==5.0.0
pytest-mock==3.14.0
python-dateutil==2.9.0.post0
bs4==0.0.1
protobuf==3.20.3
cachetools==5.5.0
pyarrow==9.0.0  # Need to keep align with azureml-dataset-runtime, otherwise may upgrade to 18.0.0 and causing unexpected behavior
mimesis
docker
tqdm
PyYAML
azure-devops
tiktoken
ijson
mldesigner==0.1.0b19
azure-identity==1.20.0
azure-ml-component==0.9.18.post2
sentence_transformers==4.1.0
ruff==0.11.9
