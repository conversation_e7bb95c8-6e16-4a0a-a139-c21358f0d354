"""
This file contains unit tests for the generate_grounding_data component.
"""

import pytest
import logging
from unittest.mock import patch
from datetime import datetime, timezone

from components.generate_grounding_data.generate_grounding_data_core import (
    generate_grounding_data_inner,
)

@pytest.fixture
def logger():
    return logging.getLogger("test_logger")

@patch("components.generate_grounding_data.generate_grounding_data_core.read_file_in_path")
@patch("components.generate_grounding_data.generate_grounding_data_core.write_file_to_storage")
@patch("components.generate_grounding_data.generate_grounding_data_core.generate_grounding_data_from_transcript")
def test_generate_grounding_data_inner(mock_generate, mock_write, mock_read, logger):
    '''
    Test for generate grounding data inner
    :param mock_generate:
    :param mock_write:
    :param mock_read:
    :param logger:
    '''
    mock_read.return_value = {
        "key1": {"transcript_key": {1:"transcript1"}},
        "key2": {"transcript_key": {2:"transcript2"}}
    }
    mock_generate.side_effect = lambda key, transcript, logger: {"data": f"grounding_data_for_{transcript}"}

    generate_grounding_data_inner(
        input_path="dummy_path",
        file_name="dummy_file.json",
        transcript_key="transcript_key",
        meeting_grounding_file_name="output_file.json",
        output_path="output_path",
        logger=logger
    )

    mock_read.assert_called_once_with("dummy_path", "dummy_file.json", logger)
    assert mock_generate.call_count == 2
    mock_write.assert_called_once()
    written_data = mock_write.call_args[0][0]
    assert written_data == {
        "key1": {"data": "grounding_data_for_{1: 'transcript1'}", "id": "key1"},
        "key2": {"data": "grounding_data_for_{2: 'transcript2'}", "id": "key2"}
    }


@patch("components.generate_grounding_data.generate_grounding_data_core.read_file_in_path")
@patch("components.generate_grounding_data.generate_grounding_data_core.write_file_to_storage")
@patch("components.generate_grounding_data.generate_grounding_data_core.generate_grounding_data_from_transcript")
@patch("components.generate_grounding_data.generate_grounding_data_core.datetime")
@patch("components.generate_grounding_data.generate_grounding_data_core.random")
def test_generate_grounding_data_with_chat_inner(mock_random, mock_datetime, mock_generate, mock_write, mock_read, logger):
    '''
    Test for generate grounding data inner with chat data
    :param mock_random: Mock for random to control random number generation
    :param mock_datetime: Mock for datetime to control time generation
    :param mock_generate: Mock for generate_grounding_data_from_transcript
    :param mock_write: Mock for write_file_to_storage
    :param mock_read: Mock for read_file_in_path
    :param logger: Logger instance
    '''
    # Mock random values for meeting time
    mock_random.randint.side_effect = [
        2023,  # year
        2,     # month
        6,     # day
        13,    # hour
        50,    # minute
        60,    # before meeting interval
        15,    # first during meeting interval
        35,    # second during meeting interval
        30,    # after meeting interval
    ]

    # Mock datetime
    fixed_time = datetime(2023, 2, 6, 13, 50, tzinfo=timezone.utc)
    mock_datetime.now.return_value = fixed_time
    mock_datetime.side_effect = datetime
    mock_datetime.timezone = timezone
    mock_datetime.return_value = fixed_time

    mock_read.return_value = {
        "key1": {
            "transcript_key": {1:"transcript1"},
            "chat_key": [
                {
                    "id": "id1",
                    "stream_id": "stream_id1",
                    "sender": "Alex Morgan",
                    "content": "Let's prepare for the meeting.",
                    "attachments": [],
                    "time_period": "before",
                    "reply_to": None
                },
                {
                    "id": "id2",
                    "stream_id": "stream_id1",
                    "sender": "Alex Morgan",
                    "content": "Great. Let's also ask for any case studies or data they can provide to support their claims.",
                    "attachments": [],
                    "time_period": "during",
                    "reply_to": None
                },
                {
                    "id": "id3",
                    "stream_id": "stream_id1",
                    "sender": "John Smith",
                    "content": "I agree with that approach.",
                    "attachments": [],
                    "time_period": "during",
                    "reply_to": "id2"
                },
                {
                    "id": "id4",
                    "stream_id": "stream_id1",
                    "sender": "Alex Morgan",
                    "content": "Thanks everyone for the productive meeting.",
                    "attachments": [],
                    "time_period": "after",
                    "reply_to": None
                }
            ],
            "transcript_args": {
                "meeting_length_min": 60
            }
        },
    }
    mock_generate.side_effect = lambda key, transcript, logger: {"data": f"grounding_data_for_{transcript}"}

    generate_grounding_data_inner(
        input_path="dummy_path",
        file_name="dummy_file.json",
        transcript_key="transcript_key",
        meeting_grounding_file_name="output_file.json",
        output_path="output_path",
        chat_key="chat_key",
        logger=logger,
        seed=42  # Use a fixed seed for reproducible tests
    )

    mock_read.assert_called_once_with("dummy_path", "dummy_file.json", logger)
    assert mock_generate.call_count == 1
    mock_write.assert_called_once()
    written_data = mock_write.call_args[0][0]
    expected_output = {
        "key1": {
            "chat_data": {
                "conversation_id": "key1_chat",
                "dialogue": [
                    {
                        "id": "id1",
                        "userName": "Alex Morgan",
                        "message": "<p>Let's prepare for the meeting.</p>",
                        "attachments": [],
                        "hasAttachments": False,
                        "replyTo": None,
                        "time": "2023-02-05T14:50:00Z",
                        "isRead": True
                    },
                    {
                        "id": "id2",
                        "userName": "Alex Morgan",
                        "message": "<p>Great. Let's also ask for any case studies or data they can provide to support their claims.</p>",
                        "attachments": [],
                        "hasAttachments": False,
                        "replyTo": None,
                        "time": "2023-02-06T14:05:00Z",
                        "isRead": True
                    },
                    {
                        "id": "id3",
                        "userName": "John Smith",
                        "message": "<p>I agree with that approach.</p>",
                        "attachments": [],
                        "hasAttachments": False,
                        "replyTo": "id2",
                        "time": "2023-02-06T14:40:00Z",
                        "isRead": True
                    },
                    {
                        "id": "id4",
                        "userName": "Alex Morgan",
                        "message": "<p>Thanks everyone for the productive meeting.</p>",
                        "attachments": [],
                        "hasAttachments": False,
                        "replyTo": None,
                        "time": "2023-02-06T15:20:00Z",
                        "isRead": True
                    }
                ],
                "participants": ["John Smith", "Alex Morgan"]
            },
            "data": "grounding_data_for_{1: 'transcript1'}",
            "id": "key1",
            "is_chat_scenario": False
        }
    }
    # Sort participants list for comparison since set doesn't preserve order
    written_data["key1"]["chat_data"]["participants"].sort()
    expected_output["key1"]["chat_data"]["participants"].sort()
    assert written_data == expected_output



def test_generate_grounding_data_inner_no_transcripts(logger):
    '''
    Test for no transcripts
    :param logger:
    '''
    with pytest.raises(ValueError, match="Transcript dummy_file.json not found"):
        generate_grounding_data_inner(
            input_path="dummy_path",
            file_name="dummy_file.json",
            transcript_key="transcript_key",
            meeting_grounding_file_name="output_file.json",
            output_path="output_path",
            logger=logger
        )

@patch("components.generate_grounding_data.generate_grounding_data_core.read_file_in_path")
def test_generate_grounding_data_inner_invalid_format(mock_read, logger):
    '''
    Test for invalid format
    :param mock_read:
    :param logger:
    '''
    mock_read.return_value = "invalid_format"
    with pytest.raises(ValueError, match="Content must be a dict for JSON format"):
        generate_grounding_data_inner(
            input_path="dummy_path",
            file_name="dummy_file.json",
            transcript_key="transcript_key",
            meeting_grounding_file_name="output_file.json",
            output_path="output_path",
            logger=logger
        )