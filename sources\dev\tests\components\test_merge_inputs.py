"""
Tests for the merge_inputs component.
"""

import logging
from unittest import mock
from unittest.mock import patch

import pytest

from components.merge_inputs.merge_inputs_core import (
    merge_data_with_overrides,
    merge_inputs_inner,
)


@pytest.fixture
def mock_logger():
    logger = mock.Mock(spec=logging.Logger)
    return logger


import pandas as pd


@pytest.mark.parametrize(
    "left_data, right_data, left_override_str, right_override_str, suffix_conflicts, expected_result",
    [
        (
            {"key1": {"name": "<PERSON>", "age": 30}},
            {"key1": {"address": "Wonderland"}, "key2": {"name": "<PERSON>", "age": 29}},
            None,
            None,
            True,
            {
                "key1": {"name": "<PERSON>", "age": 30, "address": "Wonderland"},
                "key2": {"name": "<PERSON>", "age": 29},
            },
        ),
        (
            {"key1": {"name": "<PERSON>"}},
            {"key1": {"name": "<PERSON>"}},
            None,
            None,
            True,
            {"key1": {"name": "<PERSON>", "name_right": "<PERSON>"}},
        ),
        (
            {"key1": {"name": "<PERSON>"}},
            {"key1": {"name": "<PERSON>"}},
            None,
            None,
            False,
            ValueError,
        ),
        (
            {"key1": {"name": "Alice"}},
            {"key1": {"name": "Bob"}},
            "name:first_name",
            "name:full_name",
            True,
            {"key1": {"first_name": "Alice", "full_name": "Bob"}},
        ),
    ],
)
@patch("components.merge_inputs.merge_inputs_core.read_all_files_of_type")
@patch("components.merge_inputs.merge_inputs_core.read_all_files_of_json_iterator")
@patch("components.merge_inputs.merge_inputs_core.write_file_to_storage_with_streaming")
def test_merge_inputs_inner(
    mock_write_file_to_storage_with_streaming,
    mock_read_all_files_of_json_iterator,
    mock_read_all_files_of_type,
    left_data,
    right_data,
    left_override_str,
    right_override_str,
    suffix_conflicts,
    expected_result,
    mock_logger,
):
    """
    Test the merge_inputs_inner function.
    :param mock_write_file_to_storage_with_streaming: Mock for the write_file_to_storage_with_streaming function
    :param mock_read_all_files_of_json_iterator: Mock for the read_all_files_of_json_iterator function
    :param mock_read_all_files_of_type: Mock for the read_all_files_of_type function
    :param left_data: Data in the left input file
    :param right_data: Data in the right input file
    :param left_override_str: Override string for the left input file
    :param right_override_str: Override string for the right input file
    :param suffix_conflicts: Whether to suffix conflicts
    :param expected_result: Expected result of the merge
    :param mock_logger: Mock logger
    """
    # Mock right data (small) - loaded with read_all_files_of_type
    mock_read_all_files_of_type.return_value = pd.DataFrame.from_dict(
        right_data, orient="index"
    )

    # Mock left data (large) - loaded as iterator with read_all_files_of_json_iterator
    left_df = pd.DataFrame.from_dict(left_data, orient="index")
    mock_read_all_files_of_json_iterator.return_value = iter([left_df])

    left_input_path = "mock_left_path"
    right_input_path = "mock_right_path"
    output_path = "mock_output_path"

    if expected_result is ValueError:
        # For this case, we need to mock write_file_to_storage_with_streaming to actually consume the iterator
        # so that the ValueError gets raised during the merge process
        def mock_write_that_consumes_iterator(*args, **kwargs):
            data_iterator = kwargs.get("data_iterator")
            if data_iterator:
                # Consume the iterator to trigger the ValueError
                list(data_iterator)

        mock_write_file_to_storage_with_streaming.side_effect = (
            mock_write_that_consumes_iterator
        )

        with pytest.raises(ValueError):
            merge_inputs_inner(
                left_input_path,
                right_input_path,
                left_override_str,
                right_override_str,
                file_type="json",
                output_path=output_path,
                logger=mock_logger,
                suffix_conflicts=suffix_conflicts,
                chunk_size=100,  # Small chunk size for testing
            )
    else:
        merge_inputs_inner(
            left_input_path,
            right_input_path,
            left_override_str,
            right_override_str,
            file_type="json",
            output_path=output_path,
            logger=mock_logger,
            suffix_conflicts=suffix_conflicts,
            chunk_size=100,  # Small chunk size for testing
        )

        # Verify that write_file_to_storage_with_streaming was called
        mock_write_file_to_storage_with_streaming.assert_called_once()

        # Get the data_iterator argument passed to write_file_to_storage_with_streaming and collect its output
        call_args = mock_write_file_to_storage_with_streaming.call_args
        data_iterator = call_args[1]["data_iterator"]  # keyword argument

        # Collect all chunks from the iterator and merge them
        merged_result = {}
        for chunk in data_iterator:
            merged_result.update(chunk)

        assert merged_result == expected_result


@pytest.mark.parametrize(
    "source_data, merged, override, suffix, suffix_conflicts, expected_result, expected_exception",
    [
        # Test case: No conflicts, no overrides
        (
            {"key1": {"field1": "value1"}},
            {},
            None,
            "left",
            True,
            {"key1": {"field1": "value1"}},
            None,
        ),
        # Test case: Conflict with suffix_conflicts=True
        (
            {"key1": {"field1": "value1"}},
            {"key1": {"field1": "original_value"}},
            None,
            "left",
            True,
            {"key1": {"field1": "original_value", "field1_left": "value1"}},
            None,
        ),
        # Test case: Conflict with suffix_conflicts=False (raises error)
        (
            {"key1": {"field1": "value1"}},
            {"key1": {"field1": "original_value"}},
            None,
            "left",
            False,
            None,
            ValueError,
        ),
        # Test case: Override field name
        (
            {"key1": {"field1": "value1"}},
            {},
            {"field1": "new_field1"},
            "left",
            True,
            {"key1": {"new_field1": "value1"}},
            None,
        ),
        # Test case: Override field name with conflict
        (
            {"key1": {"field1": "value1"}},
            {"key1": {"new_field1": "original_value"}},
            {"field1": "new_field1"},
            "left",
            True,
            {"key1": {"new_field1": "original_value", "new_field1_left": "value1"}},
            None,
        ),
    ],
)
def test_merge_data_with_overrides(
    mock_logger,
    source_data,
    merged,
    override,
    suffix,
    suffix_conflicts,
    expected_result,
    expected_exception,
):
    """
    Test the merge_data_with_overrides function.
    :param mock_logger: Mock logger
    :param source_data: Source data to merge
    :param merged: Existing merged data
    :param override: Override dictionary
    :param suffix: Suffix to use for conflicts
    :param suffix_conflicts: Whether to suffix conflicts
    :param expected_result: Expected result of the merge
    :param expected_exception: Expected exception type
    """
    if expected_exception:
        with pytest.raises(expected_exception):
            merge_data_with_overrides(
                source_data,
                merged,
                override,
                suffix,
                suffix_conflicts,
                1,
                1,
                mock_logger,
            )
    else:
        merge_data_with_overrides(
            source_data, merged, override, suffix, suffix_conflicts, 1, 1, mock_logger
        )
        assert merged == expected_result
