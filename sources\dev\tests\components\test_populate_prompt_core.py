"""
Tests for the parse_USR_data component.
"""

import logging
import pathlib
from unittest import mock
import pytest
import pandas as pd
from tests.test_utilities.file_utilities import compare_directories
from tools.llm_utilities import OutputPromptFormatType
from components.populate_prompt.populate_prompt_core import (
    collect_jsonl,
    fill_prompt,
    allocate_utterances_by_importance,
    convert_proportion_to_count,
    get_placeholders,
    get_prompt,
    split_list_of_items,
    split_placeholder_batch,
    split_placeholder,
    inject_from_jsonl_into_prompt,
    enum_filtering_func,
    process_all_participants,
    read_question_id_prompt_name_mapper,
)


@pytest.fixture
def mock_logger():
    logger = mock.Mock(spec=logging.Logger)
    return logger


@pytest.mark.parametrize(
    "input_data, expect_output",
    [
        (
            "standard_data_generation/seed_output/",
            [
                {
                    "id": "Domain.FINANCE_MeetingType.CLASS_SPEAKERS.2",
                    "transcript_param": {
                        "domain": "Finance",
                        "meeting_type": "Class",
                        "speakers_args": {
                            "num_of_speakers": 2,
                            "include_conf_room": False,
                            "distribution_of_speakers": "Normal",
                            "duplicate_speakers": 0,
                        },
                        "number_of_utterances": 696,
                        "meeting_length_min": 63.33273016338191,
                    },
                    "formatted_transcript": ["Alex Doe: Hello."],
                    "request_metadata": "Domain.FINANCE_MeetingType.CLASS_SPEAKERS.2_e3de293f",
                },
                {
                    "id": "Domain.SOCIAL_MeetingType.COURSE_SPEAKERS.6",
                    "transcript_param": {
                        "domain": "Social",
                        "meeting_type": "Course",
                        "speakers_args": {
                            "num_of_speakers": 6,
                            "include_conf_room": True,
                            "distribution_of_speakers": "Beta",
                            "duplicate_speakers": 1,
                        },
                        "number_of_utterances": 377,
                        "meeting_length_min": 53.87396597959753,
                    },
                    "formatted_transcript": ["John Doe: Hello."],
                    "request_metadata": "Domain.SOCIAL_MeetingType.COURSE_SPEAKERS.6_9e9a2311",
                },
            ],
        ),
        (
            "usr_data_generation/usr_data_gen/merge_inputs_async_sync_output/",
            [
                {
                    "meeting_params": {
                        "organizer_occupation": "Deposition Reporter",
                        "organizer_org": "Reliable Court Reporting",
                        "meeting_subject": "Process Improvement Meeting: Streamlining Underwriting Procedures",
                        "meeting_invite_content": "Hi everyone!",
                        "num_participants": 4,
                    },
                    "transcript_args": {
                        "number_of_utterances": 696,
                        "meeting_length_min": 63.33273016338191,
                        "speakers_args": {
                            "num_of_speakers": 4,
                            "distribution_of_speakers": "Uniform",
                        },
                    },
                    "request_metadata": "DA692406A54E947_65EC65FF714B554F",
                    "usage": {
                        "prompt_tokens": 573,
                        "completion_tokens": 274,
                        "total_tokens": 847,
                    },
                    "finish_reason": "stop",
                    "agenda": {
                        "agenda": {
                            "1": {
                                "title": "Introduction and Objectives",
                                "content": "Discuss the.",
                                "importance": 0.1,
                            },
                            "2": {
                                "title": "Review of Complex Cases",
                                "content": "Analyze.",
                                "importance": 0.2,
                            },
                            "3": {
                                "title": "New Risk Assessment Tools",
                                "content": "Explore.",
                                "importance": 0.25,
                            },
                            "4": {
                                "title": "Improving Premium Calculation Sheets",
                                "content": "Discuss.",
                                "importance": 0.2,
                            },
                            "5": {
                                "title": "Advanced Risk Assessment Software",
                                "content": "Share.",
                                "importance": 0.25,
                            },
                        },
                        "meeting_meta_data": {
                            "meeting domain": "Legal and Financial Services",
                            "meeting type": "Process Improvement Meeting",
                        },
                    },
                }
            ],
        ),
    ],
)
def test_collect_jsonl(input_data, expect_output, mock_logger):
    """
    Test the collect_jsonl component
    :param input_data: the input data
    :param expect_output: the expect output
    :param mock_logger: Mock logger object
    """

    input_data_path = pathlib.Path(__file__).parent.absolute()
    input_data_path = input_data_path.joinpath(
        "test_data",
        "test_populate_prompt_core",
        input_data,
    ).resolve()

    output = collect_jsonl(input_data_path, mock_logger, enum_filtering_func(""))
    assert output == expect_output


@pytest.mark.parametrize(
    "    prompt_path, expected_question_id_prompt_name_mapper",
    [
        (
            "e2e/data/post_response_generation/prompts",
            {
                "KeyTakeawaysWithSpeakers": {
                    "prompt_template": {
                        "messages": [
                            {
                                "role": "system",
                                "content": "Your job is to provide a summary of the meeting transcript. "
                                "Include the main topics discussed and the decisions made. "
                                "\nReference exactly one utterance ID from the transcript in its original form (e.g., "
                                '"{utt_id=123}", w/o quotes) that best supports the Key Takeaway. '
                                "When items are unified choose the most representative reference.",
                            },
                            {
                                "role": "user",
                                "content": "Transcript:\n\n{{transcript}}",
                            },
                            {
                                "role": "assistant",
                                "content": "Summary:\n\n{{model_answer_prediction_without_citations}}",
                            },
                        ]
                    },
                    "placeholders": [
                        "transcript",
                        "model_answer_prediction_without_citations",
                    ],
                    "message_count": 3,
                },
                "KeyTakeawaysWithSpeakers_with_chat": {
                    "prompt_template": {
                        "messages": [
                            {
                                "role": "system",
                                "content": "Your job is to provide a summary of the meeting transcript and chat. "
                                "Include the main topics discussed and the decisions made. "
                                "\nReference exactly one utterance ID from the transcript in its original form "
                                '(e.g., "{utt_id=123}", or one message ID from the chat in its original form '
                                '(e.g., "{IM_id=123}", w/o quotes) that best supports the Key Takeaway. '
                                "When items are unified choose the most representative reference.",
                            },
                            {
                                "role": "user",
                                "content": "Transcript:\n\n{{transcript}}\n\nChat:\n\n{{final_chat_messages}}",
                            },
                            {
                                "role": "assistant",
                                "content": "Summary:\n\n{{model_answer_prediction_without_citations}}",
                            },
                        ]
                    },
                    "placeholders": [
                        "transcript",
                        "final_chat_messages",
                        "model_answer_prediction_without_citations",
                    ],
                    "message_count": 3,
                },
                "TopicActionItems": {
                    "prompt_template": {
                        "messages": [
                            {
                                "role": "system",
                                "content": "Your job is to take notes from meeting transcripts. "
                                "Emphasize the substantial content-driven points discussed during the meeting, "
                                "providing participants and absentees with a clear understanding of the meeting's "
                                "follow-up tasks or to-do items\nReference exactly one utterance ID from the transcript in "
                                'its original form (e.g., "{utt_id=123}", w/o quotes) that best supports the Action Item. '
                                "When items are unified choose the most representative reference.",
                            },
                            {
                                "role": "user",
                                "content": "Transcript:\n\n{{transcript}}",
                            },
                            {
                                "role": "assistant",
                                "content": "Action Items:\n\n{{model_answer_prediction_without_citations}}",
                            },
                        ]
                    },
                    "placeholders": [
                        "transcript",
                        "model_answer_prediction_without_citations",
                    ],
                    "message_count": 3,
                },
                "TopicActionItems_with_chat": {
                    "prompt_template": {
                        "messages": [
                            {
                                "role": "system",
                                "content": "Your job is to take notes from meeting transcripts and chats. "
                                "Emphasize the substantial content-driven points discussed during the meeting, "
                                "providing participants and absentees with a clear understanding of the meeting's "
                                "follow-up tasks or to-do items\nReference exactly  one utterance ID from the "
                                'transcript in its original form (e.g., "{utt_id=123}", '
                                'or one message ID from the chat in its original form (e.g., "{IM_id=123}", w/o quotes)'
                                " that best supports the Action Item. When items are unified choose the most representative reference.",
                            },
                            {
                                "role": "user",
                                "content": "Transcript:\n\n{{transcript}}\n\nChat:\n\n{{final_chat_messages}}",
                            },
                            {
                                "role": "assistant",
                                "content": "Action Items:\n\n{{model_answer_prediction_without_citations}}",
                            },
                        ]
                    },
                    "placeholders": [
                        "transcript",
                        "final_chat_messages",
                        "model_answer_prediction_without_citations",
                    ],
                    "message_count": 3,
                },
                "MeetingNotes": {
                    "prompt_template": {
                        "messages": [
                            {
                                "role": "system",
                                "content": "Your job is to provide a summary of the meeting and a list of the meeting's action items. "
                                "Include the main topics discussed, decisions made, and action items assigned."
                                '\nReference exactly one utterance ID from the transcript in its original form (e.g., "{utt_id=123}",'
                                " w/o quotes) that best supports the Key Takeaway or action item. "
                                "When items are unified choose the most representative reference.",
                            },
                            {
                                "role": "user",
                                "content": "Transcript:\n\n{{transcript}}",
                            },
                            {
                                "role": "assistant",
                                "content": "Summary and Action Items:\n\n{{model_answer_prediction_without_citations}}",
                            },
                        ]
                    },
                    "placeholders": [
                        "transcript",
                        "model_answer_prediction_without_citations",
                    ],
                    "message_count": 3,
                },
                "MeetingNotes_with_chat": {
                    "prompt_template": {
                        "messages": [
                            {
                                "role": "system",
                                "content": "Your job is to provide a summary of the meeting and a list of the meeting's action items. "
                                "Include the main topics discussed, decisions made, and action items assigned."
                                "\nReference exactly one utterance ID from the transcript in its original form "
                                '(e.g., "{utt_id=123}", or one message ID from the chat in its original form '
                                '(e.g., "{IM_id=123}", w/o quotes) that best supports the Key Takeaway or action item. '
                                "When items are unified choose the most representative reference.",
                            },
                            {
                                "role": "user",
                                "content": "Transcript:\n\n{{transcript}}\n\nChat:\n\n{{final_chat_messages}}",
                            },
                            {
                                "role": "assistant",
                                "content": "Summary and Action Items:\n\n{{model_answer_prediction_without_citations}}",
                            },
                        ]
                    },
                    "placeholders": [
                        "transcript",
                        "final_chat_messages",
                        "model_answer_prediction_without_citations",
                    ],
                    "message_count": 3,
                },
                "FreeText": {
                    "prompt_template": {
                        "messages": [
                            {
                                "role": "system",
                                "content": "You are a Teams meetings chatbot (named: Meeting-Copilot) "
                                "that can extract insights and answer user requests about a meeting.",
                            },
                            {
                                "role": "user",
                                "content": "The user name is:{{user_name}}\nThe user question is:{{question}}",
                            },
                            {
                                "role": "system",
                                "content": "At the end of each informative sentence in your answer, reference exactly "
                                'one utterance ID from the transcript in its original form (e.g., "{utt_id=123}", w/o quotes) '
                                "that best supports it.\nIf the answer is not addressed by the transcript, "
                                "you will politely inform the user that there is no specific "
                                "information available in the meeting content to answer the question.",
                            },
                            {
                                "role": "user",
                                "content": "Transcript:\n\n{{transcript}}",
                            },
                            {
                                "role": "assistant",
                                "content": "Answer:\n\n{{model_answer_prediction_without_citations}}",
                            },
                        ]
                    },
                    "placeholders": [
                        "user_name",
                        "question",
                        "transcript",
                        "model_answer_prediction_without_citations",
                    ],
                    "message_count": 5,
                },
                "FreeText_with_chat": {
                    "prompt_template": {
                        "messages": [
                            {
                                "role": "system",
                                "content": "You are a Teams meetings chatbot (named: Meeting-Copilot) "
                                "that can extract insights and answer user requests about a meeting.",
                            },
                            {
                                "role": "user",
                                "content": "The user name is:{{user_name}}\nThe user question is:{{question}}",
                            },
                            {
                                "role": "system",
                                "content": "At the end of each informative sentence in your answer, reference exactly one utterance ID "
                                'from the transcript in its original form (e.g., "{utt_id=123}", or one message ID from the '
                                'chat in its original form (e.g., "{IM_id=123}", w/o quotes) that best supports it.'
                                "\nIf the answer is not addressed by the transcript and/or chat, "
                                "you will politely inform the user that there is no specific information available in the meeting "
                                "content to answer the question.",
                            },
                            {
                                "role": "user",
                                "content": "Transcript:\n\n{{transcript}}\n\nChat:\n\n{{final_chat_messages}}",
                            },
                            {
                                "role": "assistant",
                                "content": "Answer:\n\n{{model_answer_prediction_without_citations}}",
                            },
                        ]
                    },
                    "placeholders": [
                        "user_name",
                        "question",
                        "transcript",
                        "final_chat_messages",
                        "model_answer_prediction_without_citations",
                    ],
                    "message_count": 5,
                },
            },
        ),
    ],
)
def test_read_question_id_prompt_name_mapper(
    prompt_path,
    expected_question_id_prompt_name_mapper,
    mock_logger,
):
    """
    Test the read_question_id_prompt_name_mapper component
    :param prompt_path: the prompt path
    :param expected_question_id_prompt_name_mapper: the expected question id prompt name mapper
    :param mock_logger: Mock logger object
    """

    input_data_path = pathlib.Path(__file__).parent.absolute().resolve()
    prompt_path = input_data_path.joinpath(
        "..",
        prompt_path,
    ).resolve()
    question_id_prompt_mapper = read_question_id_prompt_name_mapper(
        prompt_path, mock_logger
    )
    assert question_id_prompt_mapper == expected_question_id_prompt_name_mapper


@pytest.mark.parametrize(
    "prompt_name, expect_prompt_template, expect_placeholders, expect_message_count",
    [
        (
            "generate_data_August24",
            {
                "messages": [
                    {
                        "role": "system",
                        "content": "Your task is to generate a transcript of a meeting according to a given set of properties.",
                    },
                    {
                        "role": "user",
                        "content": "The required properties for the meeting are: \n{{transcript_param}}",
                    },
                    {
                        "role": "system",
                        "content": "Meeting domain and type are used to set the context of the meeting.",
                    },
                    {
                        "role": "assistant",
                        "content": "Transcript example:{{formatted_transcript}}",
                    },
                    {
                        "role": "system",
                        "content": "The provided example is to demonstrate the format of the transcript.",
                    },
                    {"role": "assistant", "content": "The transcript is:\n"},
                ]
            },
            ["transcript_param", "formatted_transcript"],
            6,
        ),
        (
            "meeting_minutes_from_agenda_item_December15",
            {
                "messages": [
                    {
                        "role": "system",
                        "content": "Your task is to transform the provided meeting agenda item",
                    },
                    {
                        "role": "user",
                        "content": "The given agenda item is:\n{{agenda.agenda.item}}."
                        "\n The given meeting meta data is: {{agenda.meeting_meta_data}}.",
                    },
                    {"role": "system", "content": "The meeting minutes."},
                    {"role": "user", "content": "Here are examples"},
                    {"role": "system", "content": "These examples"},
                    {"role": "assistant", "content": "The meeting minutes is:\n"},
                ]
            },
            ["agenda.agenda.item", "agenda.meeting_meta_data"],
            6,
        ),
    ],
)
def test_get_prompt(
    prompt_name,
    expect_prompt_template,
    expect_placeholders,
    expect_message_count,
    mock_logger,
):
    """
    Test the get_prompt component
    :param prompt_name: the prompt name
    :param expect_prompt_template: the expect prompt template
    :param expect_placeholders: the expect placeholders
    :param expect_message_count: the expect message count
    :param mock_logger: Mock logger object
    """

    input_data_path = pathlib.Path(__file__).parent.absolute()
    input_data_path = input_data_path.joinpath("test_data", "prompts").resolve()

    prompt_template, placeholders, message_count = get_prompt(
        prompt_name,
        input_data_path,
        mock_logger,
    )

    assert prompt_template == expect_prompt_template
    assert placeholders == expect_placeholders
    assert message_count == expect_message_count


@pytest.mark.parametrize(
    "placeholders, placeholders_content, expect_output",
    [
        (
            "agenda.agenda.item",
            [
                {
                    "meeting_params": {
                        "organizer_occupation": "Deposition Reporter",
                        "organizer_org": "Reliable Court Reporting",
                        "meeting_subject": "Process Improvement Meeting: Streamlining Underwriting Procedures",
                        "meeting_invite_content": "Hi everyone!",
                        "num_participants": 4,
                    },
                    "transcript_args": {
                        "number_of_utterances": 696,
                        "meeting_length_min": 63.33273016338191,
                        "speakers_args": {
                            "num_of_speakers": 4,
                            "distribution_of_speakers": "Uniform",
                        },
                    },
                    "request_metadata": "DA692406A54E947_65EC65FF714B554F",
                    "usage": {
                        "prompt_tokens": 573,
                        "completion_tokens": 274,
                        "total_tokens": 847,
                    },
                    "finish_reason": "stop",
                    "agenda": {
                        "agenda": {
                            "1": {
                                "title": "Introduction and Objectives",
                                "content": "Discuss the.",
                                "importance": 0.1,
                            },
                            "2": {
                                "title": "Review of Complex Cases",
                                "content": "Analyze.",
                                "importance": 0.2,
                            },
                            "3": {
                                "title": "New Risk Assessment Tools",
                                "content": "Explore.",
                                "importance": 0.25,
                            },
                            "4": {
                                "title": "Improving Premium Calculation Sheets",
                                "content": "Discuss.",
                                "importance": 0.2,
                            },
                            "5": {
                                "title": "Advanced Risk Assessment Software",
                                "content": "Share.",
                                "importance": 0.25,
                            },
                        },
                        "meeting_meta_data": {
                            "meeting domain": "Legal and Financial Services",
                            "meeting type": "Process Improvement Meeting",
                        },
                    },
                }
            ],
            [
                {
                    "meeting_params": {
                        "organizer_occupation": "Deposition Reporter",
                        "organizer_org": "Reliable Court Reporting",
                        "meeting_subject": "Process Improvement Meeting: Streamlining Underwriting Procedures",
                        "meeting_invite_content": "Hi everyone!",
                        "num_participants": 4,
                    },
                    "transcript_args": {
                        "number_of_utterances": 696,
                        "meeting_length_min": 63.33273016338191,
                        "speakers_args": {
                            "num_of_speakers": 4,
                            "distribution_of_speakers": "Uniform",
                        },
                    },
                    "request_metadata": "DA692406A54E947_65EC65FF714B554F_1",
                    "usage": {
                        "prompt_tokens": 573,
                        "completion_tokens": 274,
                        "total_tokens": 847,
                    },
                    "finish_reason": "stop",
                    "agenda": {
                        "meeting_meta_data": {
                            "meeting domain": "Legal and Financial Services",
                            "meeting type": "Process Improvement Meeting",
                        }
                    },
                    "agenda.agenda.item": {
                        "title": "Introduction and Objectives",
                        "content": "Discuss the.",
                        "importance": 0.1,
                    },
                },
                {
                    "meeting_params": {
                        "organizer_occupation": "Deposition Reporter",
                        "organizer_org": "Reliable Court Reporting",
                        "meeting_subject": "Process Improvement Meeting: Streamlining Underwriting Procedures",
                        "meeting_invite_content": "Hi everyone!",
                        "num_participants": 4,
                    },
                    "transcript_args": {
                        "number_of_utterances": 696,
                        "meeting_length_min": 63.33273016338191,
                        "speakers_args": {
                            "num_of_speakers": 4,
                            "distribution_of_speakers": "Uniform",
                        },
                    },
                    "request_metadata": "DA692406A54E947_65EC65FF714B554F_2",
                    "usage": {
                        "prompt_tokens": 573,
                        "completion_tokens": 274,
                        "total_tokens": 847,
                    },
                    "finish_reason": "stop",
                    "agenda": {
                        "meeting_meta_data": {
                            "meeting domain": "Legal and Financial Services",
                            "meeting type": "Process Improvement Meeting",
                        }
                    },
                    "agenda.agenda.item": {
                        "title": "Review of Complex Cases",
                        "content": "Analyze.",
                        "importance": 0.2,
                    },
                },
                {
                    "meeting_params": {
                        "organizer_occupation": "Deposition Reporter",
                        "organizer_org": "Reliable Court Reporting",
                        "meeting_subject": "Process Improvement Meeting: Streamlining Underwriting Procedures",
                        "meeting_invite_content": "Hi everyone!",
                        "num_participants": 4,
                    },
                    "transcript_args": {
                        "number_of_utterances": 696,
                        "meeting_length_min": 63.33273016338191,
                        "speakers_args": {
                            "num_of_speakers": 4,
                            "distribution_of_speakers": "Uniform",
                        },
                    },
                    "request_metadata": "DA692406A54E947_65EC65FF714B554F_3",
                    "usage": {
                        "prompt_tokens": 573,
                        "completion_tokens": 274,
                        "total_tokens": 847,
                    },
                    "finish_reason": "stop",
                    "agenda": {
                        "meeting_meta_data": {
                            "meeting domain": "Legal and Financial Services",
                            "meeting type": "Process Improvement Meeting",
                        }
                    },
                    "agenda.agenda.item": {
                        "title": "New Risk Assessment Tools",
                        "content": "Explore.",
                        "importance": 0.25,
                    },
                },
                {
                    "meeting_params": {
                        "organizer_occupation": "Deposition Reporter",
                        "organizer_org": "Reliable Court Reporting",
                        "meeting_subject": "Process Improvement Meeting: Streamlining Underwriting Procedures",
                        "meeting_invite_content": "Hi everyone!",
                        "num_participants": 4,
                    },
                    "transcript_args": {
                        "number_of_utterances": 696,
                        "meeting_length_min": 63.33273016338191,
                        "speakers_args": {
                            "num_of_speakers": 4,
                            "distribution_of_speakers": "Uniform",
                        },
                    },
                    "request_metadata": "DA692406A54E947_65EC65FF714B554F_4",
                    "usage": {
                        "prompt_tokens": 573,
                        "completion_tokens": 274,
                        "total_tokens": 847,
                    },
                    "finish_reason": "stop",
                    "agenda": {
                        "meeting_meta_data": {
                            "meeting domain": "Legal and Financial Services",
                            "meeting type": "Process Improvement Meeting",
                        }
                    },
                    "agenda.agenda.item": {
                        "title": "Improving Premium Calculation Sheets",
                        "content": "Discuss.",
                        "importance": 0.2,
                    },
                },
                {
                    "meeting_params": {
                        "organizer_occupation": "Deposition Reporter",
                        "organizer_org": "Reliable Court Reporting",
                        "meeting_subject": "Process Improvement Meeting: Streamlining Underwriting Procedures",
                        "meeting_invite_content": "Hi everyone!",
                        "num_participants": 4,
                    },
                    "transcript_args": {
                        "number_of_utterances": 696,
                        "meeting_length_min": 63.33273016338191,
                        "speakers_args": {
                            "num_of_speakers": 4,
                            "distribution_of_speakers": "Uniform",
                        },
                    },
                    "request_metadata": "DA692406A54E947_65EC65FF714B554F_5",
                    "usage": {
                        "prompt_tokens": 573,
                        "completion_tokens": 274,
                        "total_tokens": 847,
                    },
                    "finish_reason": "stop",
                    "agenda": {
                        "meeting_meta_data": {
                            "meeting domain": "Legal and Financial Services",
                            "meeting type": "Process Improvement Meeting",
                        }
                    },
                    "agenda.agenda.item": {
                        "title": "Advanced Risk Assessment Software",
                        "content": "Share.",
                        "importance": 0.25,
                    },
                },
            ],
        ),
    ],
)
def test_split_placeholder_batch(
    placeholders, placeholders_content, expect_output, mock_logger
):
    """
    Test the split_placeholder component
    :param placeholders: the placeholders
    :param placeholders_content: the placeholders content
    :param expect_output: the expect output
    :param mock_logger: Mock logger object
    """

    output = split_placeholder_batch(placeholders, placeholders_content, mock_logger)
    assert output == expect_output


@pytest.mark.parametrize(
    "placeholders, placeholders_content, expect_output",
    [
        (
            "agenda.agenda.item",
            {
                "meeting_params": {
                    "organizer_occupation": "Deposition Reporter",
                    "organizer_org": "Reliable Court Reporting",
                    "meeting_subject": "Process Improvement Meeting: Streamlining Underwriting Procedures",
                    "meeting_invite_content": "Hi everyone!",
                    "num_participants": 4,
                },
                "transcript_args": {
                    "number_of_utterances": 696,
                    "meeting_length_min": 63.33273016338191,
                    "speakers_args": {
                        "num_of_speakers": 4,
                        "distribution_of_speakers": "Uniform",
                    },
                },
                "request_metadata": "DA692406A54E947_65EC65FF714B554F",
                "usage": {
                    "prompt_tokens": 573,
                    "completion_tokens": 274,
                    "total_tokens": 847,
                },
                "finish_reason": "stop",
                "agenda": {
                    "agenda": {
                        "1": {
                            "title": "Introduction and Objectives",
                            "content": "Discuss the.",
                            "importance": 0.1,
                        },
                        "2": {
                            "title": "Review of Complex Cases",
                            "content": "Analyze.",
                            "importance": 0.2,
                        },
                        "3": {
                            "title": "New Risk Assessment Tools",
                            "content": "Explore.",
                            "importance": 0.25,
                        },
                        "4": {
                            "title": "Improving Premium Calculation Sheets",
                            "content": "Discuss.",
                            "importance": 0.2,
                        },
                        "5": {
                            "title": "Advanced Risk Assessment Software",
                            "content": "Share.",
                            "importance": 0.25,
                        },
                    },
                    "meeting_meta_data": {
                        "meeting domain": "Legal and Financial Services",
                        "meeting type": "Process Improvement Meeting",
                    },
                },
            },
            [
                {
                    "meeting_params": {
                        "organizer_occupation": "Deposition Reporter",
                        "organizer_org": "Reliable Court Reporting",
                        "meeting_subject": "Process Improvement Meeting: Streamlining Underwriting Procedures",
                        "meeting_invite_content": "Hi everyone!",
                        "num_participants": 4,
                    },
                    "transcript_args": {
                        "number_of_utterances": 696,
                        "meeting_length_min": 63.33273016338191,
                        "speakers_args": {
                            "num_of_speakers": 4,
                            "distribution_of_speakers": "Uniform",
                        },
                    },
                    "request_metadata": "DA692406A54E947_65EC65FF714B554F_1",
                    "usage": {
                        "prompt_tokens": 573,
                        "completion_tokens": 274,
                        "total_tokens": 847,
                    },
                    "finish_reason": "stop",
                    "agenda": {
                        "meeting_meta_data": {
                            "meeting domain": "Legal and Financial Services",
                            "meeting type": "Process Improvement Meeting",
                        }
                    },
                    "agenda.agenda.item": {
                        "title": "Introduction and Objectives",
                        "content": "Discuss the.",
                        "importance": 0.1,
                    },
                },
                {
                    "meeting_params": {
                        "organizer_occupation": "Deposition Reporter",
                        "organizer_org": "Reliable Court Reporting",
                        "meeting_subject": "Process Improvement Meeting: Streamlining Underwriting Procedures",
                        "meeting_invite_content": "Hi everyone!",
                        "num_participants": 4,
                    },
                    "transcript_args": {
                        "number_of_utterances": 696,
                        "meeting_length_min": 63.33273016338191,
                        "speakers_args": {
                            "num_of_speakers": 4,
                            "distribution_of_speakers": "Uniform",
                        },
                    },
                    "request_metadata": "DA692406A54E947_65EC65FF714B554F_2",
                    "usage": {
                        "prompt_tokens": 573,
                        "completion_tokens": 274,
                        "total_tokens": 847,
                    },
                    "finish_reason": "stop",
                    "agenda": {
                        "meeting_meta_data": {
                            "meeting domain": "Legal and Financial Services",
                            "meeting type": "Process Improvement Meeting",
                        }
                    },
                    "agenda.agenda.item": {
                        "title": "Review of Complex Cases",
                        "content": "Analyze.",
                        "importance": 0.2,
                    },
                },
                {
                    "meeting_params": {
                        "organizer_occupation": "Deposition Reporter",
                        "organizer_org": "Reliable Court Reporting",
                        "meeting_subject": "Process Improvement Meeting: Streamlining Underwriting Procedures",
                        "meeting_invite_content": "Hi everyone!",
                        "num_participants": 4,
                    },
                    "transcript_args": {
                        "number_of_utterances": 696,
                        "meeting_length_min": 63.33273016338191,
                        "speakers_args": {
                            "num_of_speakers": 4,
                            "distribution_of_speakers": "Uniform",
                        },
                    },
                    "request_metadata": "DA692406A54E947_65EC65FF714B554F_3",
                    "usage": {
                        "prompt_tokens": 573,
                        "completion_tokens": 274,
                        "total_tokens": 847,
                    },
                    "finish_reason": "stop",
                    "agenda": {
                        "meeting_meta_data": {
                            "meeting domain": "Legal and Financial Services",
                            "meeting type": "Process Improvement Meeting",
                        }
                    },
                    "agenda.agenda.item": {
                        "title": "New Risk Assessment Tools",
                        "content": "Explore.",
                        "importance": 0.25,
                    },
                },
                {
                    "meeting_params": {
                        "organizer_occupation": "Deposition Reporter",
                        "organizer_org": "Reliable Court Reporting",
                        "meeting_subject": "Process Improvement Meeting: Streamlining Underwriting Procedures",
                        "meeting_invite_content": "Hi everyone!",
                        "num_participants": 4,
                    },
                    "transcript_args": {
                        "number_of_utterances": 696,
                        "meeting_length_min": 63.33273016338191,
                        "speakers_args": {
                            "num_of_speakers": 4,
                            "distribution_of_speakers": "Uniform",
                        },
                    },
                    "request_metadata": "DA692406A54E947_65EC65FF714B554F_4",
                    "usage": {
                        "prompt_tokens": 573,
                        "completion_tokens": 274,
                        "total_tokens": 847,
                    },
                    "finish_reason": "stop",
                    "agenda": {
                        "meeting_meta_data": {
                            "meeting domain": "Legal and Financial Services",
                            "meeting type": "Process Improvement Meeting",
                        }
                    },
                    "agenda.agenda.item": {
                        "title": "Improving Premium Calculation Sheets",
                        "content": "Discuss.",
                        "importance": 0.2,
                    },
                },
                {
                    "meeting_params": {
                        "organizer_occupation": "Deposition Reporter",
                        "organizer_org": "Reliable Court Reporting",
                        "meeting_subject": "Process Improvement Meeting: Streamlining Underwriting Procedures",
                        "meeting_invite_content": "Hi everyone!",
                        "num_participants": 4,
                    },
                    "transcript_args": {
                        "number_of_utterances": 696,
                        "meeting_length_min": 63.33273016338191,
                        "speakers_args": {
                            "num_of_speakers": 4,
                            "distribution_of_speakers": "Uniform",
                        },
                    },
                    "request_metadata": "DA692406A54E947_65EC65FF714B554F_5",
                    "usage": {
                        "prompt_tokens": 573,
                        "completion_tokens": 274,
                        "total_tokens": 847,
                    },
                    "finish_reason": "stop",
                    "agenda": {
                        "meeting_meta_data": {
                            "meeting domain": "Legal and Financial Services",
                            "meeting type": "Process Improvement Meeting",
                        }
                    },
                    "agenda.agenda.item": {
                        "title": "Advanced Risk Assessment Software",
                        "content": "Share.",
                        "importance": 0.25,
                    },
                },
            ],
        ),
    ],
)
def test_split_placeholder(
    placeholders, placeholders_content, expect_output, mock_logger
):
    """
    Test the split_placeholder component
    :param placeholders: the placeholders
    :param placeholders_content: the placeholders content
    :param expect_output: the expect output
    :param mock_logger: Mock logger object
    """

    output = split_placeholder(placeholders, placeholders_content, mock_logger)
    assert output == expect_output


@pytest.mark.parametrize(
    "prompt_template, placeholders, data, expect_output",
    [
        (
            {
                "messages": [
                    {
                        "role": "system",
                        "content": "Your task is to generate a transcript of a meeting according to a given set of properties.",
                    },
                    {
                        "role": "user",
                        "content": "The required properties for the meeting are: \n{{transcript_param}}",
                    },
                    {
                        "role": "system",
                        "content": "Meeting domain and type are used to set the context of the meeting.",
                    },
                    {
                        "role": "assistant",
                        "content": "Transcript example:{{formatted_transcript}}",
                    },
                    {
                        "role": "system",
                        "content": "The provided example is to demonstrate the format of the transcript.",
                    },
                    {"role": "assistant", "content": "The transcript is:\n"},
                ]
            },
            ["transcript_param", "formatted_transcript"],
            {
                "id": "Domain.FINANCE_MeetingType.CLASS_SPEAKERS.2",
                "transcript_param": {
                    "domain": "Finance",
                    "meeting_type": "Class",
                    "speakers_args": {
                        "num_of_speakers": 2,
                        "include_conf_room": False,
                        "distribution_of_speakers": "Normal",
                        "duplicate_speakers": 0,
                    },
                    "number_of_utterances": 696,
                    "meeting_length_min": 63.33273016338191,
                },
                "formatted_transcript": ["Alex Doe: Hello."],
                "request_metadata": "Domain.FINANCE_MeetingType.CLASS_SPEAKERS.2_e3de293f",
            },
            {
                "messages": [
                    {
                        "role": "system",
                        "content": "Your task is to generate a transcript of a meeting according to a given set of properties.",
                    },
                    {
                        "role": "user",
                        "content": "The required properties for the meeting are: \n{'domain': 'Finance', 'meeting_type': 'Class', \
'speakers_args': {'num_of_speakers': 2, 'include_conf_room': False, 'distribution_of_speakers': 'Normal', 'duplicate_speakers': 0}, \
'number_of_utterances': 696, 'meeting_length_min': 63.33273016338191}",
                    },
                    {
                        "role": "system",
                        "content": "Meeting domain and type are used to set the context of the meeting.",
                    },
                    {
                        "role": "assistant",
                        "content": "Transcript example:['Alex Doe: Hello.']",
                    },
                    {
                        "role": "system",
                        "content": "The provided example is to demonstrate the format of the transcript.",
                    },
                    {"role": "assistant", "content": "The transcript is:\n"},
                ]
            },
        ),
    ],
)
def test_fill_prompt(prompt_template, placeholders, data, expect_output, mock_logger):
    """
    Test the fill_prompt component
    :param prompt_template: the prompt template
    :param placeholders: the placeholders
    :param data: the data
    :param expect_output: the expect output
    :param mock_logger: Mock logger object
    """

    output = fill_prompt(prompt_template, placeholders, data, mock_logger)
    assert output == expect_output


@pytest.mark.parametrize(
    "data, expect_output",
    [
        (
            {
                "meeting_params": {
                    "organizer_occupation": "Deposition Reporter",
                    "organizer_org": "Reliable Court Reporting",
                    "meeting_subject": "Process Improvement Meeting: Streamlining Underwriting Procedures",
                    "meeting_invite_content": "Hi everyone!",
                    "num_participants": 4,
                },
                "transcript_args": {
                    "number_of_utterances": 696,
                    "meeting_length_min": 63.33273016338191,
                    "speakers_args": {
                        "num_of_speakers": 4,
                        "distribution_of_speakers": "Uniform",
                    },
                },
                "request_metadata": "DA692406A54E947_65EC65FF714B554F_5",
                "usage": {
                    "prompt_tokens": 573,
                    "completion_tokens": 274,
                    "total_tokens": 847,
                },
                "finish_reason": "stop",
                "agenda": {
                    "meeting_meta_data": {
                        "meeting domain": "Legal and Financial Services",
                        "meeting type": "Process Improvement Meeting",
                    }
                },
                "agenda.agenda.item": {
                    "title": "Advanced Risk Assessment Software",
                    "content": "Share.",
                    "importance": 0.25,
                },
            },
            {
                "meeting_params": {
                    "organizer_occupation": "Deposition Reporter",
                    "organizer_org": "Reliable Court Reporting",
                    "meeting_subject": "Process Improvement Meeting: Streamlining Underwriting Procedures",
                    "meeting_invite_content": "Hi everyone!",
                    "num_participants": 4,
                },
                "transcript_args": {
                    "number_of_utterances": 696,
                    "meeting_length_min": 63.33273016338191,
                    "speakers_args": {
                        "num_of_speakers": 4,
                        "distribution_of_speakers": "Uniform",
                    },
                },
                "request_metadata": "DA692406A54E947_65EC65FF714B554F_5",
                "usage": {
                    "prompt_tokens": 573,
                    "completion_tokens": 274,
                    "total_tokens": 847,
                },
                "finish_reason": "stop",
                "agenda": {
                    "meeting_meta_data": {
                        "meeting domain": "Legal and Financial Services",
                        "meeting type": "Process Improvement Meeting",
                    }
                },
                "agenda.agenda.item": {
                    "title": "Advanced Risk Assessment Software",
                    "content": "Share.",
                    "number of utterances": 174,
                    "number of events": 14,
                },
            },
        ),
    ],
)
def test_convert_proportion_to_count(data, expect_output):
    """
    Test the convert_proportion_to_count component
    :param data: the data
    :param expect_output: the expect output
    """

    convert_proportion_to_count(data)
    output = data
    assert output == expect_output


@pytest.mark.parametrize(
    "data, expect_output",
    [
        (
            {
                "usage": {},
                "meeting_params": {
                    "participants": [
                        "David Patel",
                        "Doretta Lew",
                        "Karen Williams",
                        "Rachel Kim",
                        "Sherley Rennie",
                        "Thomas Nguyen",
                    ]
                },
                "transcript_args": {
                    "number_of_utterances": 350,
                    "meeting_length_min": 31.832814744170527,
                    "speakers_args": {"distribution_of_speakers": "Exponential"},
                },
                "request_metadata": "AA_BB",
                "usage_right": {},
                "finish_reason_right": "stop",
                "agenda": {"agenda": {}, "meeting_meta_data": {}},
                "meeting_minutes": {
                    "meeting_minutes": {
                        "1": {
                            "speakers": "David Patel, All participants",
                            "content": (
                                "The team agreed on the next steps: Doretta "
                                "Lew will research funding options, Thomas Nguyen will set up platform demos,"
                                " Sherley Rennie will connect with "
                                "developers of the successful platform, "
                                "and a committee will be formed to oversee the implementation. The "
                                "meeting concluded with a plan to reconvene in two weeks to review progress."
                            ),
                            "volume": "long",
                        },
                        "2": {
                            "speakers": "All participants",
                            "content": (
                                "The team agreed on the next steps: Doretta "
                                "Lew will research funding options, Thomas Nguyen will set up platform demos,"
                                " Sherley Rennie will connect with "
                                "developers of the successful platform, "
                                "and a committee will be formed to oversee the implementation. The "
                                "meeting concluded with a plan to reconvene in two weeks to review progress."
                            ),
                            "volume": "long",
                        },
                        "3": {
                            "speakers": "All",
                            "content": (
                                "The team agreed on the next steps: Doretta "
                                "Lew will research funding options, Thomas Nguyen will set up platform demos,"
                                " Sherley Rennie will connect with "
                                "developers of the successful platform, "
                                "and a committee will be formed to oversee the implementation. The "
                                "meeting concluded with a plan to reconvene in two weeks to review progress."
                            ),
                            "volume": "long",
                        },
                        "4": {
                            "speakers": "Sherley Rennie, All",
                            "content": (
                                "The team agreed on the next steps: Doretta "
                                "Lew will research funding options, Thomas Nguyen will set up platform demos,"
                                " Sherley Rennie will connect with "
                                "developers of the successful platform, "
                                "and a committee will be formed to oversee the implementation. The "
                                "meeting concluded with a plan to reconvene in two weeks to review progress."
                            ),
                            "volume": "long",
                        },
                    }
                },
            },
            {
                "usage": {},
                "meeting_params": {
                    "participants": [
                        "David Patel",
                        "Doretta Lew",
                        "Karen Williams",
                        "Rachel Kim",
                        "Sherley Rennie",
                        "Thomas Nguyen",
                    ]
                },
                "transcript_args": {
                    "number_of_utterances": 350,
                    "meeting_length_min": 31.832814744170527,
                    "speakers_args": {"distribution_of_speakers": "Exponential"},
                },
                "request_metadata": "AA_BB",
                "usage_right": {},
                "finish_reason_right": "stop",
                "agenda": {"agenda": {}, "meeting_meta_data": {}},
                "meeting_minutes": {
                    "meeting_minutes": {
                        "1": {
                            "speakers": [
                                "David Patel",
                                "Doretta Lew",
                                "Karen Williams",
                                "Rachel Kim",
                                "Sherley Rennie",
                                "Thomas Nguyen",
                            ],
                            "content": (
                                "The team agreed on the next steps: Doretta Lew will research funding options,"
                                " Thomas Nguyen will "
                                "set up platform demos, Sherley Rennie will connect"
                                " with developers of the successful platform, and a committee "
                                "will be formed to oversee the implementation. "
                                "The meeting concluded with a plan to reconvene in two weeks "
                                "to review progress."
                            ),
                            "volume": "long",
                        },
                        "2": {
                            "speakers": [
                                "David Patel",
                                "Doretta Lew",
                                "Karen Williams",
                                "Rachel Kim",
                                "Sherley Rennie",
                                "Thomas Nguyen",
                            ],
                            "content": (
                                "The team agreed on the next steps: Doretta Lew will research funding options,"
                                " Thomas Nguyen will "
                                "set up platform demos, Sherley Rennie will connect"
                                " with developers of the successful platform, and a committee "
                                "will be formed to oversee the implementation. "
                                "The meeting concluded with a plan to reconvene in two weeks "
                                "to review progress."
                            ),
                            "volume": "long",
                        },
                        "3": {
                            "speakers": [
                                "David Patel",
                                "Doretta Lew",
                                "Karen Williams",
                                "Rachel Kim",
                                "Sherley Rennie",
                                "Thomas Nguyen",
                            ],
                            "content": (
                                "The team agreed on the next steps: Doretta Lew will research funding options,"
                                " Thomas Nguyen will "
                                "set up platform demos, Sherley Rennie will connect"
                                " with developers of the successful platform, and a committee "
                                "will be formed to oversee the implementation. "
                                "The meeting concluded with a plan to reconvene in two weeks "
                                "to review progress."
                            ),
                            "volume": "long",
                        },
                        "4": {
                            "speakers": [
                                "David Patel",
                                "Doretta Lew",
                                "Karen Williams",
                                "Rachel Kim",
                                "Sherley Rennie",
                                "Thomas Nguyen",
                            ],
                            "content": (
                                "The team agreed on the next steps: Doretta Lew will research funding options,"
                                " Thomas Nguyen will "
                                "set up platform demos, Sherley Rennie will connect"
                                " with developers of the successful platform, and a committee "
                                "will be formed to oversee the implementation. "
                                "The meeting concluded with a plan to reconvene in two weeks "
                                "to review progress."
                            ),
                            "volume": "long",
                        },
                    }
                },
            },
        ),
    ],
)
def test_process_all_participants_filter(data, expect_output, mock_logger):
    """
    Test the convert_proportion_to_count component
    :param data: the data
    :param expect_output: the expect output
    :param mock_logger: Mock logger object
    """
    data = pd.DataFrame([data])
    output = process_all_participants(data, mock_logger)
    # Convert DataFrame back to dictionary
    output_dict = list(output.to_dict(orient="records"))
    output_dict = output_dict[0]
    assert output_dict == expect_output


@pytest.mark.parametrize(
    "data, expect_output",
    [
        (
            {
                "usage": {},
                "meeting_params": {},
                "transcript_args": {
                    "number_of_utterances": 350,
                    "meeting_length_min": 31.832814744170527,
                    "speakers_args": {"distribution_of_speakers": "Exponential"},
                },
                "request_metadata": "AA_BB",
                "usage_right": {},
                "finish_reason_right": "stop",
                "agenda": {"agenda": {}, "meeting_meta_data": {}},
                "meeting_minutes": {
                    "meeting_minutes": {
                        "1": {
                            "speakers": "All participants",
                            "content": (
                                "The team agreed on the next steps: Doretta "
                                "Lew will research funding options, Thomas Nguyen will set up platform demos,"
                                " Sherley Rennie will connect with "
                                "developers of the successful platform, "
                                "and a committee will be formed to oversee the implementation. The "
                                "meeting concluded with a plan to reconvene in two weeks to review progress."
                            ),
                            "volume": "long",
                        }
                    }
                },
            },
            {},
        ),
    ],
)
def test_process_all_participants_filter_no_participants(
    data, expect_output, mock_logger
):
    """
    Test the convert_proportion_to_count component
    :param data: the data
    :param expect_output: the expect output
    :param mock_logger: Mock logger object
    """
    data = pd.DataFrame([data])
    expect_output = pd.DataFrame([expect_output])
    output = process_all_participants(data, mock_logger)
    # Convert DataFrame back to dictionary
    assert output.empty == expect_output.empty


@pytest.mark.parametrize(
    "data, expect_output",
    [
        (
            {
                "usage": {},
                "transcript_args": {
                    "number_of_utterances": 350,
                    "meeting_length_min": 31.832814744170527,
                    "speakers_args": {"distribution_of_speakers": "Exponential"},
                },
                "request_metadata": "AA_BB",
                "usage_right": {},
                "finish_reason_right": "stop",
                "agenda": {"agenda": {}, "meeting_meta_data": {}},
                "meeting_minutes": {
                    "meeting_minutes": {
                        "1": {
                            "speakers": "All participants",
                            "content": (
                                "The team agreed on the next steps: Doretta "
                                "Lew will research funding options, Thomas Nguyen will set up platform demos,"
                                " Sherley Rennie will connect with "
                                "developers of the successful platform, "
                                "and a committee will be formed to oversee the implementation. The "
                                "meeting concluded with a plan to reconvene in two weeks to review progress."
                            ),
                            "volume": "long",
                        }
                    }
                },
            },
            {},
        ),
    ],
)
def test_process_all_participants_filter_no_meeting_param(
    data, expect_output, mock_logger
):
    """
    Test the convert_proportion_to_count component
    :param data: the data
    :param expect_output: the expect output
    :param mock_logger: Mock logger object
    """
    data = pd.DataFrame([data])
    expect_output = pd.DataFrame([expect_output])
    output = process_all_participants(data, mock_logger)
    # Convert DataFrame back to dictionary
    assert output.empty == expect_output.empty


@pytest.mark.parametrize(
    "data, total_utterances, expect_output",
    [
        (
            {
                "title": "Advanced Risk Assessment Software",
                "content": "Share.",
                "importance": 0.25,
            },
            696,
            {
                "title": "Advanced Risk Assessment Software",
                "content": "Share.",
                "number of utterances": 174,
                "number of events": 14,
            },
        ),
    ],
)
def test_allocate_utterances_by_importance(data, total_utterances, expect_output):
    """
    Test the allocate_utterances_by_importance component
    :param data: the data
    :param total_utterances: the total utterances
    :param expect_output: the expect output
    """

    allocate_utterances_by_importance(data, total_utterances, is_generate_events=True)
    assert data == expect_output


@pytest.mark.parametrize(
    "placeholder, placeholders_content, expect_output",
    [
        (
            "agenda.agenda.item",
            [
                {
                    "meeting_params": {
                        "organizer_occupation": "Deposition Reporter",
                        "organizer_org": "Reliable Court Reporting",
                        "meeting_subject": "Process Improvement Meeting: Streamlining Underwriting Procedures",
                        "meeting_invite_content": "Hi everyone!",
                        "num_participants": 4,
                    },
                    "transcript_args": {
                        "number_of_utterances": 696,
                        "meeting_length_min": 63.33273016338191,
                        "speakers_args": {
                            "num_of_speakers": 4,
                            "distribution_of_speakers": "Uniform",
                        },
                    },
                    "request_metadata": "DA692406A54E947_65EC65FF714B554F",
                    "usage": {
                        "prompt_tokens": 573,
                        "completion_tokens": 274,
                        "total_tokens": 847,
                    },
                    "finish_reason": "stop",
                    "agenda": {
                        "agenda": {
                            "1": {
                                "title": "Introduction and Objectives",
                                "content": "Discuss the.",
                                "importance": 0.1,
                            },
                            "2": {
                                "title": "Review of Complex Cases",
                                "content": "Analyze.",
                                "importance": 0.2,
                            },
                            "3": {
                                "title": "New Risk Assessment Tools",
                                "content": "Explore.",
                                "importance": 0.25,
                            },
                            "4": {
                                "title": "Improving Premium Calculation Sheets",
                                "content": "Discuss.",
                                "importance": 0.2,
                            },
                            "5": {
                                "title": "Advanced Risk Assessment Software",
                                "content": "Share.",
                                "importance": 0.25,
                            },
                        },
                        "meeting_meta_data": {
                            "meeting domain": "Legal and Financial Services",
                            "meeting type": "Process Improvement Meeting",
                        },
                    },
                }
            ],
            [
                {
                    "meeting_params": {
                        "organizer_occupation": "Deposition Reporter",
                        "organizer_org": "Reliable Court Reporting",
                        "meeting_subject": "Process Improvement Meeting: Streamlining Underwriting Procedures",
                        "meeting_invite_content": "Hi everyone!",
                        "num_participants": 4,
                    },
                    "transcript_args": {
                        "number_of_utterances": 696,
                        "meeting_length_min": 63.33273016338191,
                        "speakers_args": {
                            "num_of_speakers": 4,
                            "distribution_of_speakers": "Uniform",
                        },
                    },
                    "request_metadata": "DA692406A54E947_65EC65FF714B554F_1",
                    "usage": {
                        "prompt_tokens": 573,
                        "completion_tokens": 274,
                        "total_tokens": 847,
                    },
                    "finish_reason": "stop",
                    "agenda": {
                        "agenda": {
                            "1": {
                                "title": "Introduction and Objectives",
                                "content": "Discuss the.",
                                "importance": 0.1,
                            },
                            "2": {
                                "title": "Review of Complex Cases",
                                "content": "Analyze.",
                                "importance": 0.2,
                            },
                            "3": {
                                "title": "New Risk Assessment Tools",
                                "content": "Explore.",
                                "importance": 0.25,
                            },
                            "4": {
                                "title": "Improving Premium Calculation Sheets",
                                "content": "Discuss.",
                                "importance": 0.2,
                            },
                            "5": {
                                "title": "Advanced Risk Assessment Software",
                                "content": "Share.",
                                "importance": 0.25,
                            },
                        },
                        "meeting_meta_data": {
                            "meeting domain": "Legal and Financial Services",
                            "meeting type": "Process Improvement Meeting",
                        },
                    },
                    "agenda.agenda.item": {
                        "title": "Introduction and Objectives",
                        "content": "Discuss the.",
                        "importance": 0.1,
                    },
                },
                {
                    "meeting_params": {
                        "organizer_occupation": "Deposition Reporter",
                        "organizer_org": "Reliable Court Reporting",
                        "meeting_subject": "Process Improvement Meeting: Streamlining Underwriting Procedures",
                        "meeting_invite_content": "Hi everyone!",
                        "num_participants": 4,
                    },
                    "transcript_args": {
                        "number_of_utterances": 696,
                        "meeting_length_min": 63.33273016338191,
                        "speakers_args": {
                            "num_of_speakers": 4,
                            "distribution_of_speakers": "Uniform",
                        },
                    },
                    "request_metadata": "DA692406A54E947_65EC65FF714B554F_2",
                    "usage": {
                        "prompt_tokens": 573,
                        "completion_tokens": 274,
                        "total_tokens": 847,
                    },
                    "finish_reason": "stop",
                    "agenda": {
                        "agenda": {
                            "1": {
                                "title": "Introduction and Objectives",
                                "content": "Discuss the.",
                                "importance": 0.1,
                            },
                            "2": {
                                "title": "Review of Complex Cases",
                                "content": "Analyze.",
                                "importance": 0.2,
                            },
                            "3": {
                                "title": "New Risk Assessment Tools",
                                "content": "Explore.",
                                "importance": 0.25,
                            },
                            "4": {
                                "title": "Improving Premium Calculation Sheets",
                                "content": "Discuss.",
                                "importance": 0.2,
                            },
                            "5": {
                                "title": "Advanced Risk Assessment Software",
                                "content": "Share.",
                                "importance": 0.25,
                            },
                        },
                        "meeting_meta_data": {
                            "meeting domain": "Legal and Financial Services",
                            "meeting type": "Process Improvement Meeting",
                        },
                    },
                    "agenda.agenda.item": {
                        "title": "Review of Complex Cases",
                        "content": "Analyze.",
                        "importance": 0.2,
                    },
                },
                {
                    "meeting_params": {
                        "organizer_occupation": "Deposition Reporter",
                        "organizer_org": "Reliable Court Reporting",
                        "meeting_subject": "Process Improvement Meeting: Streamlining Underwriting Procedures",
                        "meeting_invite_content": "Hi everyone!",
                        "num_participants": 4,
                    },
                    "transcript_args": {
                        "number_of_utterances": 696,
                        "meeting_length_min": 63.33273016338191,
                        "speakers_args": {
                            "num_of_speakers": 4,
                            "distribution_of_speakers": "Uniform",
                        },
                    },
                    "request_metadata": "DA692406A54E947_65EC65FF714B554F_3",
                    "usage": {
                        "prompt_tokens": 573,
                        "completion_tokens": 274,
                        "total_tokens": 847,
                    },
                    "finish_reason": "stop",
                    "agenda": {
                        "agenda": {
                            "1": {
                                "title": "Introduction and Objectives",
                                "content": "Discuss the.",
                                "importance": 0.1,
                            },
                            "2": {
                                "title": "Review of Complex Cases",
                                "content": "Analyze.",
                                "importance": 0.2,
                            },
                            "3": {
                                "title": "New Risk Assessment Tools",
                                "content": "Explore.",
                                "importance": 0.25,
                            },
                            "4": {
                                "title": "Improving Premium Calculation Sheets",
                                "content": "Discuss.",
                                "importance": 0.2,
                            },
                            "5": {
                                "title": "Advanced Risk Assessment Software",
                                "content": "Share.",
                                "importance": 0.25,
                            },
                        },
                        "meeting_meta_data": {
                            "meeting domain": "Legal and Financial Services",
                            "meeting type": "Process Improvement Meeting",
                        },
                    },
                    "agenda.agenda.item": {
                        "title": "New Risk Assessment Tools",
                        "content": "Explore.",
                        "importance": 0.25,
                    },
                },
                {
                    "meeting_params": {
                        "organizer_occupation": "Deposition Reporter",
                        "organizer_org": "Reliable Court Reporting",
                        "meeting_subject": "Process Improvement Meeting: Streamlining Underwriting Procedures",
                        "meeting_invite_content": "Hi everyone!",
                        "num_participants": 4,
                    },
                    "transcript_args": {
                        "number_of_utterances": 696,
                        "meeting_length_min": 63.33273016338191,
                        "speakers_args": {
                            "num_of_speakers": 4,
                            "distribution_of_speakers": "Uniform",
                        },
                    },
                    "request_metadata": "DA692406A54E947_65EC65FF714B554F_4",
                    "usage": {
                        "prompt_tokens": 573,
                        "completion_tokens": 274,
                        "total_tokens": 847,
                    },
                    "finish_reason": "stop",
                    "agenda": {
                        "agenda": {
                            "1": {
                                "title": "Introduction and Objectives",
                                "content": "Discuss the.",
                                "importance": 0.1,
                            },
                            "2": {
                                "title": "Review of Complex Cases",
                                "content": "Analyze.",
                                "importance": 0.2,
                            },
                            "3": {
                                "title": "New Risk Assessment Tools",
                                "content": "Explore.",
                                "importance": 0.25,
                            },
                            "4": {
                                "title": "Improving Premium Calculation Sheets",
                                "content": "Discuss.",
                                "importance": 0.2,
                            },
                            "5": {
                                "title": "Advanced Risk Assessment Software",
                                "content": "Share.",
                                "importance": 0.25,
                            },
                        },
                        "meeting_meta_data": {
                            "meeting domain": "Legal and Financial Services",
                            "meeting type": "Process Improvement Meeting",
                        },
                    },
                    "agenda.agenda.item": {
                        "title": "Improving Premium Calculation Sheets",
                        "content": "Discuss.",
                        "importance": 0.2,
                    },
                },
                {
                    "meeting_params": {
                        "organizer_occupation": "Deposition Reporter",
                        "organizer_org": "Reliable Court Reporting",
                        "meeting_subject": "Process Improvement Meeting: Streamlining Underwriting Procedures",
                        "meeting_invite_content": "Hi everyone!",
                        "num_participants": 4,
                    },
                    "transcript_args": {
                        "number_of_utterances": 696,
                        "meeting_length_min": 63.33273016338191,
                        "speakers_args": {
                            "num_of_speakers": 4,
                            "distribution_of_speakers": "Uniform",
                        },
                    },
                    "request_metadata": "DA692406A54E947_65EC65FF714B554F_5",
                    "usage": {
                        "prompt_tokens": 573,
                        "completion_tokens": 274,
                        "total_tokens": 847,
                    },
                    "finish_reason": "stop",
                    "agenda": {
                        "agenda": {
                            "1": {
                                "title": "Introduction and Objectives",
                                "content": "Discuss the.",
                                "importance": 0.1,
                            },
                            "2": {
                                "title": "Review of Complex Cases",
                                "content": "Analyze.",
                                "importance": 0.2,
                            },
                            "3": {
                                "title": "New Risk Assessment Tools",
                                "content": "Explore.",
                                "importance": 0.25,
                            },
                            "4": {
                                "title": "Improving Premium Calculation Sheets",
                                "content": "Discuss.",
                                "importance": 0.2,
                            },
                            "5": {
                                "title": "Advanced Risk Assessment Software",
                                "content": "Share.",
                                "importance": 0.25,
                            },
                        },
                        "meeting_meta_data": {
                            "meeting domain": "Legal and Financial Services",
                            "meeting type": "Process Improvement Meeting",
                        },
                    },
                    "agenda.agenda.item": {
                        "title": "Advanced Risk Assessment Software",
                        "content": "Share.",
                        "importance": 0.25,
                    },
                },
            ],
        ),
    ],
)
def test_split_list_of_items(placeholder, placeholders_content, expect_output):
    """
    Test the split_list_of_items component
    :param placeholder: the placeholder
    :param placeholders_content: the placeholders content
    :param expect_output: the expect output
    """
    placeholder_split = placeholder.split(".")
    output = split_list_of_items(
        placeholder, placeholders_content, placeholder_split[:-1], mock_logger
    )
    assert output == expect_output


@pytest.mark.parametrize(
    "prompt_template, expect_output",
    [
        (
            {
                "messages": [
                    {
                        "role": "system",
                        "content": "Your task is to generate a transcript of a meeting according to a given set of properties.",
                    },
                    {
                        "role": "user",
                        "content": "The required properties for the meeting are: \n{{transcript_param}}",
                    },
                    {
                        "role": "system",
                        "content": "Meeting domain and type are used to set the context of the meeting.",
                    },
                    {
                        "role": "assistant",
                        "content": "Transcript example:{{formatted_transcript}}",
                    },
                    {
                        "role": "system",
                        "content": "The provided example is to demonstrate the format of the transcript.",
                    },
                    {"role": "assistant", "content": "The transcript is:\n"},
                ]
            },
            ["transcript_param", "formatted_transcript"],
        ),
    ],
)
def test_get_placeholders(prompt_template, expect_output):
    """
    Test the get_placeholders component
    :param prompt_template: the prompt template
    :param expect_output: the expect output
    """
    output = get_placeholders(prompt_template)
    assert output == expect_output


@pytest.mark.parametrize(
    "input_data, prompt_name, prompts_path, max_tokens, temperature, output_prompt_format, "
    "evaluate_mode, do_shuffle, seed, expect_output_path, expect_completion_path",
    [
        (
            "usr_data_generation/usr_data_gen/merge_inputs_async_sync_output/",
            "meeting_minutes_from_agenda_item_December15",
            "prompts/gen_meeting_minutes_item_prompt",
            4000,
            0,
            OutputPromptFormatType.LLM_API_ASYNC_CHAT_COMPLETION,
            False,
            False,
            42,
            "usr_meeting_minutes_data_generation/meeting_minutes_item/gen_prompt_async_output/",
            "usr_meeting_minutes_data_generation/meeting_minutes_item/gen_prompt_async_prompt_output/",
        ),
        (
            "usr_data_generation/usr_data_gen/merge_inputs_async_sync_output/no_existing_folder",
            "meeting_minutes_from_agenda_item_December15",
            "prompts/gen_meeting_minutes_item_prompt",
            4000,
            0,
            OutputPromptFormatType.LLM_API_ASYNC_CHAT_COMPLETION,
            False,
            False,
            42,
            None,
            None,
        ),
    ],
)
def test_inject_from_jsonl_into_prompt(
    tmp_path,
    input_data,
    prompt_name,
    prompts_path,
    max_tokens,
    temperature,
    output_prompt_format,
    evaluate_mode,
    do_shuffle,
    seed,
    mock_logger,
    expect_output_path,
    expect_completion_path,
):
    """
    Test the inject_from_jsonl_into_prompt component
    :param tmp_path: The temporary path
    :param input_data: The input data
    :param prompt_name: The prompt name
    :param prompts_path: The prompts path
    :param max_tokens: The max tokens
    :param temperature: The temperature
    :param output_prompt_format: The output prompt format
    :param evaluate_mode: The evaluate mode
    :param do_shuffle: The shuffle flag
    :param seed: The random seed
    :param mock_logger: Mock logger object
    :param expect_output_path: The expected output path
    :param expect_completion_path: The expected completion path
    """

    input_data = (
        pathlib.Path(__file__)
        .parent.absolute()
        .joinpath("..", "e2e", "data", "data_generation", input_data)
        .resolve()
    )

    prompts_path = (
        pathlib.Path(__file__)
        .parent.absolute()
        .joinpath("..", "e2e", "data", "data_generation", prompts_path)
        .resolve()
    )

    output_path = tmp_path.joinpath("gen_prompt_async_output")
    completion_path = tmp_path.joinpath("gen_prompt_async_prompt_output")
    # inject the prompts into the prompt
    inject_from_jsonl_into_prompt(
        input_data,
        prompt_name,
        prompts_path,
        output_path,
        completion_path,
        max_tokens,
        temperature,
        output_prompt_format,
        "",
        evaluate_mode,
        do_shuffle,
        seed,
        mock_logger,
    )
    if expect_output_path is not None:
        expect_output_path = (
            pathlib.Path(__file__)
            .parent.absolute()
            .joinpath("..", "e2e", "data", "data_generation", expect_output_path)
            .resolve()
        )

        expect_completion_path = (
            pathlib.Path(__file__)
            .parent.absolute()
            .joinpath("..", "e2e", "data", "data_generation", expect_completion_path)
            .resolve()
        )

        compare_directories(output_path, expect_output_path)
        compare_directories(completion_path, expect_completion_path)
    else:
        mock_logger.info.assert_any_call(
            "No placeholders_content found after filtering"
        )


@pytest.mark.parametrize(
    "input_data, prompt_name, prompts_path, max_tokens, temperature, "
    "output_prompt_format, evaluate_mode, do_shuffle, seed, expect_output_path, expect_completion_path",
    [
        (  # KeyTakeawaysWithSpeakers, TopicActionItems
            "input_for_populate_the_prompt",
            "",
            "prompts",
            4000,
            0,
            OutputPromptFormatType.FT,
            False,
            False,
            42,
            "output",
            "output_completion",
        ),
        (  # KeyTakeawaysWithSpeakers, TopicActionItems with chat
            "input_for_populate_the_prompt_with_chat",
            "",
            "prompts",
            4000,
            0,
            OutputPromptFormatType.FT,
            False,
            True,
            42,
            "output_with_chat",
            "output_completion_with_chat",
        ),
    ],
)
def test_inject_from_jsonl_into_prompt_ft_pipeline(
    tmp_path,
    input_data,
    prompt_name,
    prompts_path,
    max_tokens,
    temperature,
    output_prompt_format,
    evaluate_mode,
    do_shuffle,
    seed,
    mock_logger,
    expect_output_path,
    expect_completion_path,
):
    """
    Test the inject_from_jsonl_into_prompt component
    :param tmp_path: The temporary path
    :param input_data: The input data
    :param prompt_name: The prompt name
    :param prompts_path: The prompts path
    :param max_tokens: The max tokens
    :param temperature: The temperature
    :param output_prompt_format: The output prompt format
    :param evaluate_mode: The evaluate mode
    :param do_shuffle: Whether to shuffle the data
    :param seed: The random seed
    :param mock_logger: Mock logger object
    :param expect_output_path: The expected output path
    :param expect_completion_path: The expected completion path
    """

    input_data = (
        pathlib.Path(__file__)
        .parent.absolute()
        .joinpath(
            "test_data", "test_inject_from_jsonl_into_prompt_ft_pipeline", input_data
        )
        .resolve()
    )

    prompts_path = (
        pathlib.Path(__file__)
        .parent.absolute()
        .joinpath("..", "e2e", "data", "post_response_generation", prompts_path)
        .resolve()
    )

    output_path = tmp_path.joinpath("gen_prompt_async_output")
    completion_path = tmp_path.joinpath("gen_prompt_async_prompt_output")
    # inject the prompts into the prompt
    inject_from_jsonl_into_prompt(
        input_data,
        prompt_name,
        prompts_path,
        output_path,
        completion_path,
        max_tokens,
        temperature,
        output_prompt_format,
        "",
        evaluate_mode,
        do_shuffle,
        seed,
        mock_logger,
    )
    if expect_output_path is not None:
        expect_output_path = (
            pathlib.Path(__file__)
            .parent.absolute()
            .joinpath(
                "test_data",
                "test_inject_from_jsonl_into_prompt_ft_pipeline",
                expect_output_path,
            )
            .resolve()
        )

        expect_completion_path = (
            pathlib.Path(__file__)
            .parent.absolute()
            .joinpath(
                "test_data",
                "test_inject_from_jsonl_into_prompt_ft_pipeline",
                expect_completion_path,
            )
            .resolve()
        )

        compare_directories(output_path, expect_output_path)
        compare_directories(completion_path, expect_completion_path)
    else:
        mock_logger.info.assert_any_call(
            "No placeholders_content found after filtering"
        )
