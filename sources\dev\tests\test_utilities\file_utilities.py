"""
This module contains utilities related to file operations used by tests.
"""

import difflib
import filecmp
import json
import logging
import math
import os
import pathlib
from typing import List, Any, Union

from pandas.testing import assert_frame_equal

logger = logging.getLogger(__name__)


class FloatTolerantJSONEncoder(json.JSONEncoder):
    """
    Custom JSON encoder that rounds floating-point numbers to a reasonable precision
    to avoid floating-point representation inconsistencies.
    """

    def encode(self, obj):
        if isinstance(obj, float):
            # Use more aggressive rounding for small numbers to avoid precision issues
            return super().encode(self._normalize_float(obj))
        return super().encode(obj)

    def iterencode(self, obj, _one_shot=False):
        """Encode the given object and yield each string representation as available."""
        if _one_shot and isinstance(obj, (dict, list)):
            obj = self._normalize_floats(obj)
        for chunk in super().iterencode(obj, _one_shot):
            yield chunk

    def _normalize_float(self, value):
        """Normalize a single float value with appropriate precision."""
        if abs(value) < 1e-4:
            # For very small numbers, round to 9 significant digits to avoid precision issues
            if value == 0:
                return 0.0
            # Use string formatting to control precision for very small numbers
            return float(f"{value:.9g}")
        else:
            # For regular numbers, round to 12 decimal places
            return round(value, 12)

    def _normalize_floats(self, obj):
        """Recursively normalize floating-point numbers in the object."""
        if isinstance(obj, dict):
            return {k: self._normalize_floats(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._normalize_floats(item) for item in obj]
        elif isinstance(obj, float):
            return self._normalize_float(obj)
        else:
            return obj


def are_floats_equal(
    a: float, b: float, rel_tol: float = 1e-9, abs_tol: float = 1e-12
) -> bool:
    """
    Check if two floating-point numbers are approximately equal.

    :param a: First floating-point number
    :param b: Second floating-point number
    :param rel_tol: Relative tolerance parameter
    :param abs_tol: Absolute tolerance parameter
    :return: True if the numbers are approximately equal
    """
    return math.isclose(a, b, rel_tol=rel_tol, abs_tol=abs_tol)


def normalize_numerical_data(data: Any) -> Any:
    """
    Recursively normalize floating-point numbers in data structures to handle precision issues.

    :param data: The data to normalize (can be dict, list, float, or other types)
    :return: Data with normalized floating-point numbers
    """
    if isinstance(data, dict):
        return {k: normalize_numerical_data(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [normalize_numerical_data(item) for item in data]
    elif isinstance(data, float):
        # Use the same normalization logic as FloatTolerantJSONEncoder
        if abs(data) < 1e-4:
            # For very small numbers, round to 9 significant digits to avoid precision issues
            if data == 0:
                return 0.0
            # Use string formatting to control precision for very small numbers
            return float(f"{data:.9g}")
        else:
            # For regular numbers, round to 12 decimal places
            return round(data, 12)
    else:
        return data


def remove_keys_recursively(data: Any, keys_to_remove: List[str]) -> Any:
    """
    Recursively remove specified keys from a JSON object, regardless of their location.

    :param data: The JSON data (dict or list).
    :param keys_to_remove: A list of key names to remove from the JSON object.
    :return: A new JSON object with specified keys removed.
    """
    if isinstance(data, dict):
        return {
            k: remove_keys_recursively(v, keys_to_remove)
            for k, v in data.items()
            if k not in keys_to_remove
        }
    elif isinstance(data, list):
        return [remove_keys_recursively(item, keys_to_remove) for item in data]
    else:
        return data


def normalize_and_sort(data: Any) -> str:
    """
    Normalize the JSON data by sorting keys and formatting with indentation.
    Uses a custom encoder to handle floating-point precision issues.

    :param data: The JSON data (dict or list).
    :return: A normalized JSON string.
    """
    # Normalize floating-point numbers first to handle precision issues
    normalized_data = normalize_numerical_data(data)
    sorted_json = json.dumps(
        normalized_data, sort_keys=True, indent=2, cls=FloatTolerantJSONEncoder
    )
    return normalize_newlines(sorted_json)


def process_json_content(
    data: Union[dict, list],
    keys_to_ignore: List[str],
    sort_list: bool = False,
    sort_key_func: Any = None,
) -> Union[str, List[str]]:
    """
    Process JSON content by removing specified keys, optionally sorting lists,
    and normalizing the JSON structure.

    :param data: The JSON data (dict or list).
    :param keys_to_ignore: List of keys to ignore during processing.
    :param sort_list: Whether to sort lists of JSON objects.
    :param sort_key_func: A function to determine the sort key for list items.
    :return: Normalized JSON string or list of strings.
    """
    # Remove specified keys
    processed_data = remove_keys_recursively(data, keys_to_ignore)

    # If sorting is required for lists (e.g., JSONL), sort them
    if sort_list and isinstance(processed_data, list) and sort_key_func:
        processed_data = sorted(processed_data, key=sort_key_func)

    # Normalize the JSON structure
    normalized_json = normalize_and_sort(processed_data)

    return normalized_json


def generate_diff(
    expected: Union[str, List[str]],
    generated: Union[str, List[str]],
    fromfile: str,
    tofile: str,
) -> str:
    """
    Generate a unified diff between two JSON strings or lists of JSON strings.

    :param expected: The expected normalized JSON string or list of strings.
    :param generated: The generated normalized JSON string or list of strings.
    :param fromfile: Label for the expected file.
    :param tofile: Label for the generated file.
    :return: A string containing the unified diff.
    """
    if isinstance(expected, list) and isinstance(generated, list):
        diff = difflib.unified_diff(
            expected,
            generated,
            lineterm="",
            fromfile=fromfile,
            tofile=tofile,
        )
    else:
        diff = difflib.unified_diff(
            expected.splitlines(),
            generated.splitlines(),
            lineterm="",
            fromfile=fromfile,
            tofile=tofile,
        )
    return "\n".join(diff)


def compare_json_files(generated_file, expected_file, keys_to_ignore: List[str] = None):
    """
    Compare two JSON files while ignoring specified keys, regardless of their location.
    Log details if differences are found.

    :param generated_file: Path to the generated JSON file.
    :param expected_file: Path to the expected JSON file.
    :param keys_to_ignore: List of keys to ignore during comparison.
    """
    keys_to_ignore = keys_to_ignore or []

    with (
        open(generated_file, "r", encoding="utf-8") as gen_file,
        open(expected_file, "r", encoding="utf-8") as exp_file,
    ):
        gen_json = json.load(gen_file)
        exp_json = json.load(exp_file)

        # Process JSON content
        gen_normalized = process_json_content(gen_json, keys_to_ignore)
        exp_normalized = process_json_content(exp_json, keys_to_ignore)

        if gen_normalized != exp_normalized:
            diff_output = generate_diff(
                exp_normalized,
                gen_normalized,
                fromfile=str(expected_file),
                tofile=str(generated_file),
            )
            logger.debug(
                f"JSON files differ between '{generated_file}' and '{expected_file}':\n{diff_output}"
            )
            assert False, (
                f"JSON files differ: {generated_file} vs {expected_file}\n"
                f"Differences:\n{diff_output}"
            )


def compare_jsonl_files(
    generated_file, expected_file, keys_to_ignore: List[str] = None
):
    """
    Compare two JSONL files while ignoring specified keys in each JSON object.
    Log details if differences are found.

    :param generated_file: Path to the generated JSONL file.
    :param expected_file: Path to the expected JSONL file.
    :param keys_to_ignore: List of keys to ignore during comparison.
    """
    keys_to_ignore = keys_to_ignore or []

    with (
        open(generated_file, "r", encoding="utf-8") as gen_file,
        open(expected_file, "r", encoding="utf-8") as exp_file,
    ):
        # Load JSON objects from each line
        gen_content = [json.loads(line) for line in gen_file.readlines()]
        exp_content = [json.loads(line) for line in exp_file.readlines()]

        def sort_key_func(item):
            # Use request_metadata for sorting if it exists, otherwise use a placeholder
            return item.get("request_metadata", "")

        # Process and sort JSON content
        gen_normalized = process_json_content(
            gen_content, keys_to_ignore, sort_list=True, sort_key_func=sort_key_func
        )
        exp_normalized = process_json_content(
            exp_content, keys_to_ignore, sort_list=True, sort_key_func=sort_key_func
        )

        if gen_normalized != exp_normalized:
            diff_output = generate_diff(
                exp_normalized.splitlines(),
                gen_normalized.splitlines(),
                fromfile=str(expected_file),
                tofile=str(generated_file),
            )
            logger.debug(
                f"JSONL files differ between '{generated_file}' and '{expected_file}':\n{diff_output}"
            )
            assert False, (
                f"JSONL files differ: {generated_file} vs {expected_file}\n"
                f"Differences:\n{diff_output}"
            )


def compare_text_files(generated_file, expected_file, keys_to_ignore: List[str] = None):
    """
    Compare two text files or JSON files, ignoring differences in line endings and formatting.
    If the files are JSON or JSONL, it can ignore specified keys.
    Log details if differences are found.

    :param generated_file: Path to the generated file.
    :param expected_file: Path to the expected file.
    :param keys_to_ignore: List of keys to ignore if files are JSON or JSONL.
    """
    # Handle JSON and JSONL files explicitly
    if generated_file.suffix == ".json":
        compare_json_files(generated_file, expected_file, keys_to_ignore=keys_to_ignore)
    elif generated_file.suffix == ".jsonl":
        compare_jsonl_files(
            generated_file, expected_file, keys_to_ignore=keys_to_ignore
        )
    else:
        with (
            open(generated_file, "r", encoding="utf-8") as gen_file,
            open(expected_file, "r", encoding="utf-8") as exp_file,
        ):
            gen_lines = gen_file.readlines()
            exp_lines = exp_file.readlines()

            # Normalize line endings and ignore trailing whitespace
            gen_lines = [normalize_newlines(line) for line in gen_lines]
            exp_lines = [normalize_newlines(line) for line in exp_lines]

            if gen_lines != exp_lines:
                diff_output = generate_diff(
                    exp_lines,
                    gen_lines,
                    fromfile=str(expected_file),
                    tofile=str(generated_file),
                )

                # Log the diff for debugging purposes
                logger.debug(
                    f"Text files differ between '{generated_file}' and '{expected_file}':\n{diff_output}"
                )

                # Raise an assertion with the diff details
                assert False, (
                    f"Text files differ: {generated_file} vs {expected_file}\nDifferences:\n{diff_output}"
                )


def compare_binary_files(generated_file, expected_file):
    """
    Compare binary files directly, ignoring metadata.
    Log details if differences are found.
    :param generated_file: Path to the generated file.
    :param expected_file: Path to the expected file.
    """
    if not filecmp.cmp(generated_file, expected_file, shallow=False):
        logger.debug(f"Binary files differ: {generated_file} vs {expected_file}")

        # Raise an assertion with details about the binary file mismatch
        assert False, f"Binary files differ: {generated_file} vs {expected_file}"


def assert_dataframe_equal(out, expectout, check_order=False):
    """
    This function compares two pd.DataFrame data for equality, optionally checking row order.

    :param out: First input pd.DataFrame data.
    :param expectout: Second input pd.DataFrame data.
    :param check_order: If True, ensures that row order matches as well.
    """
    # Ensure both DataFrames have the same columns
    if not set(out.columns) == set(expectout.columns):
        raise AssertionError(
            f"Column mismatch detected:\n"
            f"Columns in 'out' but not in 'expectout': {set(out.columns) - set(expectout.columns)}\n"
            f"Columns in 'expectout' but not in 'out': {set(expectout.columns) - set(out.columns)}"
        )

    # Align columns to ensure consistent order
    common_columns = sorted(out.columns)
    out_aligned = out[common_columns]
    expectout_aligned = expectout[common_columns]

    if check_order:
        # Directly compare without sorting
        assert_frame_equal(
            expectout_aligned.reset_index(drop=True), out_aligned.reset_index(drop=True)
        )
    else:
        # Sort values and compare
        out_sorted = out_aligned.sort_values(by=common_columns).reset_index(drop=True)
        expectout_sorted = expectout_aligned.sort_values(by=common_columns).reset_index(
            drop=True
        )
        assert_frame_equal(expectout_sorted, out_sorted, check_like=True)


def normalize_newlines(content):
    """
    Normalize all line endings to Unix-style newlines (\n) and remove trailing whitespace in the provided content,
    handling both real and escaped newlines.
    :param content: The content to be normalized.
    """
    if isinstance(content, str):
        # Handle actual newlines and escaped newlines, and remove trailing whitespace
        # Normalize real newlines
        content = content.rstrip().replace(
            "\r\n", "\n"
        )  # Normalize Windows-style line endings
        content = content.replace("\r", "\n")  # Normalize isolated carriage returns

        # Normalize escaped newlines
        content = content.replace(
            "\\r\\n", "\n"
        )  # Normalize escaped Windows-style line endings
        content = content.replace("\\r", "\\n")  # Convert escaped \r to escaped \n
        content = content.replace(
            "\\n", "\n"
        )  # Normalize escaped Unix-style line endings

        return content if content.endswith("\n") else content + "\n"
    elif isinstance(content, list):
        return [normalize_newlines(item) for item in content]
    elif isinstance(content, dict):
        return {key: normalize_newlines(value) for key, value in content.items()}
    return content


def normalize_and_sort_jsonl(content):
    """
    Normalize and sort a list of JSON objects by a specified key if it exists, or by a default key otherwise.
    :param content: List of JSON objects.
    :return: Sorted list of JSON objects.
    """

    def sort_key(item):
        # Use `entryId` for sorting if it exists, otherwise use a placeholder
        return item.get("entryId", "")

    # Sort the content based on the extracted key
    return sorted(content, key=sort_key)


def compare_directories(
    output_path, expected_output_path, keys_to_ignore: List[str] = None
):
    """
    Compare files in two directories and assert that they are identical, ignoring system-specific differences.
    This function compares the file contents, ignoring metadata like timestamps, line endings, and file permissions.
    :param output_path: The path to the output directory.
    :param expected_output_path: The path to the expected output directory.
    :param keys_to_ignore: List of keys to ignore when comparing JSON files.
    """
    output_path = pathlib.Path(output_path).resolve()
    expected_output_path = pathlib.Path(expected_output_path).resolve()

    for root, _, files in os.walk(output_path):
        relative_path = pathlib.Path(root).relative_to(output_path)
        corresponding_expected_dir = expected_output_path / relative_path

        # Ensure the expected directory exists
        assert corresponding_expected_dir.exists(), (
            f"Expected directory '{corresponding_expected_dir}' does not exist."
        )

        for file_name in files:
            generated_file = pathlib.Path(root) / file_name
            expected_file = corresponding_expected_dir / file_name

            # Ensure the expected file exists
            assert expected_file.exists(), (
                f"Expected file '{expected_file}' does not exist."
            )

            # Compare based on file type
            if generated_file.suffix in [".txt", ".json", ".csv", ".tsv", ".jsonl"]:
                if generated_file.suffix == ".json":
                    compare_json_files(
                        generated_file, expected_file, keys_to_ignore=keys_to_ignore
                    )
                elif generated_file.suffix == ".jsonl":
                    compare_jsonl_files(
                        generated_file, expected_file, keys_to_ignore=keys_to_ignore
                    )
                else:
                    compare_text_files(
                        generated_file, expected_file, keys_to_ignore=keys_to_ignore
                    )
            else:
                compare_binary_files(generated_file, expected_file)


def compare_files_ignore_eol(generated_file, expected_file):
    """
    This function compares whether two files are equal.
    :param generated_file: the generated file.
    :param expected_file: the expected file.
    """
    with open(generated_file, "r") as gen_file, open(expected_file, "r") as exp_file:
        gen_content = gen_file.read().replace("\r\n", "\n").replace("\r", "\n")
        exp_content = exp_file.read().replace("\r\n", "\n").replace("\r", "\n")
        return gen_content == exp_content
