"""unit tests for the functions in tools/io_helpers.py"""

import json
import logging
import os
import pathlib
from unittest.mock import MagicMock, patch, mock_open

import dask.dataframe as dd
import numpy as np
import pandas as pd
import pytest

from tests.test_utilities.file_utilities import (
    assert_dataframe_equal,
    compare_files_ignore_eol,
)
from tools.io_helpers import (
    write_file_to_storage,
    list_file_type_in_path,
    read_all_files_of_type,
    read_all_files_of_json_iterator,
    reading_file_to_pd,
    read_file_in_path,
    load_tsv,
    load_tsv_dask,
    ensure_suffix,
    convert_json_array_to_jsonl,
    load_text,
    read_file_with_fallback_encoding,
    make_json_serializable,
    write_chunks_files_to_storage,
    chunk_data,
    reading_json_dicts,
    reading_json,
)


@pytest.fixture
def mock_logger():
    return MagicMock(spec=logging.Logger)


@pytest.mark.parametrize(
    "content, suffix, intype",
    [
        ([{"item1": 1}, {"item2": 2}], "json", "list"),
        ({"key": "value"}, "json", "dict"),
        ("strtest", "json", "str"),
        ([{"item1": 1}, {"item2": 2}], "jsonl", "list"),
        ({"key": "value"}, "jsonl", "dict"),
        (pd.DataFrame({"name": ["Alice", "Bob"], "age": [30, 25]}), "csv", "dataframe"),
        (pd.DataFrame({"name": ["Alice", "Bob"], "age": [30, 25]}), "tsv", "dataframe"),
        ("strtest", "csv", "str"),
        ("strtest", "txt", "str"),
    ],
)
def test_write_file_to_storage(tmp_path, content, suffix, intype):
    """
    Test the write_file_to_storage in tools/io_helpers.py
    :param tmp_path: the write path
    :param content: the content to write
    :param suffix: the suffix
    :param intype: the content type
    """
    logger = MagicMock()
    output_name = f"{intype}.{suffix}"
    current_path = pathlib.Path(__file__).parent.absolute()
    ref_file_path = current_path.joinpath("test_data", "io_helper").resolve()
    write_file_to_storage(content, tmp_path, output_name, logger, suffix)
    if suffix == "json":
        if isinstance(content, str):
            logger.error.assert_any_call(
                "Content must not be a str for JSON format",
            )
        else:
            assert os.path.exists(os.path.join(tmp_path, output_name)), (
                f"Output file {output_name} was not created"
            )
            assert compare_files_ignore_eol(
                os.path.join(tmp_path, output_name),
                os.path.join(ref_file_path, output_name),
            )
    elif suffix == "jsonl":
        if isinstance(content, list):
            assert os.path.exists(os.path.join(tmp_path, output_name)), (
                f"Output file {output_name} was not created"
            )
            assert compare_files_ignore_eol(
                os.path.join(tmp_path, output_name),
                os.path.join(ref_file_path, output_name),
            )
        else:
            logger.error.assert_any_call(
                "Content must be a list for JSONL format",
            )
    elif suffix in ["tsv", "csv"]:
        assert os.path.exists(os.path.join(tmp_path, output_name)), (
            f"Output file {output_name} was not created"
        )
        assert compare_files_ignore_eol(
            os.path.join(tmp_path, output_name),
            os.path.join(ref_file_path, output_name),
        )
    else:
        logger.error.assert_any_call(
            f"Suffix {suffix} is not supported",
        )


@pytest.mark.parametrize(
    "infile, filetype, return_full_path, expect_out",
    [
        ("", "json", True, ["list.json", "dict.json"]),
        ("", "json", False, ["list.json", "dict.json"]),
        ("", "txt", True, []),
        ("", "txt", False, []),
        ("dict.json", "json", True, "dict.json"),
        ("dict.json", "json", False, "dict.json"),
        ("dict.json", "txt", True, "dict.json.txt"),
        ("dict.json", "txt", False, "dict.json.txt"),
    ],
)
def test_list_file_type_in_path(infile, filetype, return_full_path, expect_out):
    """
    Test the list_file_type_in_path in tools/io_helpers.py
    :param infile: input file
    :param filetype: file types
    :param return_full_path: whether return full path
    :param expect_out: expect output
    """
    logger = MagicMock()
    current_path = pathlib.Path(__file__).parent.absolute()
    inpath = current_path.joinpath("test_data", "io_helper").resolve()
    if infile != "":
        inpath = inpath.joinpath(infile)

    out_results = list_file_type_in_path(
        str(inpath), filetype, logger, return_full_path
    )

    if os.path.isdir(inpath):
        if isinstance(expect_out, list):
            if return_full_path:
                expect_out = [os.path.join(inpath, eo) for eo in expect_out]
            assert sorted(out_results) == sorted(expect_out), (
                "out_results is not equal to expect_out"
            )
        else:
            assert out_results == expect_out, "out_results is not equal to expect_out"
    else:
        # default return_full_path true
        if expect_out is not None:
            expect_out = [str(inpath.parent.joinpath(expect_out).resolve())]
            assert out_results == expect_out, "out_results is not equal to expect_out"
        else:
            logger.error.assert_called_once_with(
                f"Path {inpath} is not a valid directory or file",
            )


@pytest.mark.parametrize(
    "filename, filetype, expect_out",
    [
        ("dict.json", "json", pd.DataFrame({"key": ["value"]})),
        (
            "list.json",
            "json",
            pd.DataFrame(
                {0: {"item1": 1, "item2": None}, 1: {"item1": None, "item2": 2}}
            ),
        ),
        (
            "newlist.jsonl",
            "jsonl",
            pd.DataFrame({"name": ["John", "Alice"], "age": [30, 25]}),
        ),
        (
            "dict_convert.jsonl",
            "jsonl",
            pd.DataFrame({"key": ["value"]}),
        ),
        (
            "list.jsonl",
            "jsonl",
            pd.DataFrame([{"item1": 1}, {"item2": 2}]),
        ),
        (
            "dataframe.csv",
            "csv",
            pd.DataFrame({"name": ["Alice", "Bob"], "age": [30, 25]}),
        ),
        (
            "dataframe.tsv",
            "tsv",
            pd.DataFrame({"name": ["Alice", "Bob"], "age": [30, 25]}),
        ),
    ],
)
def test_reading_file_to_pd(filename, filetype, expect_out):
    """
    Test the reading_file_to_pd in tools/io_helpers.py
    :param filename: input file
    :param filetype: file types
    :param expect_out: expect output
    """
    logger = MagicMock()
    current_path = pathlib.Path(__file__).parent.absolute()
    inpath = current_path.joinpath("test_data", "io_helper", filename).resolve()

    out_results = reading_file_to_pd(filetype, logger, inpath)
    try:
        assert_dataframe_equal(out_results, expect_out)
    except AssertionError as e:
        raise AssertionError(f"DataFrames comparison failed: {e}")


@pytest.mark.parametrize(
    "filename, filetype,  expected_exception, expect_message",
    [
        (
            "faulty_data.tsv",
            "tsv",
            pd.errors.ParserError,
            "Error tokenizing data. C error: Expected 3 fields in line 3, saw 4\n",
        ),
    ],
)
def test_reading_file_to_pd_invalid(
    filename, filetype, expected_exception, expect_message
):
    """
    Test the reading_file_to_pd function. Invalid args
    :param filename: the input file
    :param filetype: the file type
    :param expected_exception: the expected exception
    :param expect_message: the expected exception message
    """
    logger = MagicMock()
    current_path = pathlib.Path(__file__).parent.absolute()
    inpath = current_path.joinpath(
        "test_data", "io_helper", "faultry", filename
    ).resolve()
    with pytest.raises(expected_exception, match=expect_message):
        _ = reading_file_to_pd(filetype, logger, inpath)


@pytest.mark.parametrize(
    "filetype, expect_out",
    [
        (
            "jsonl",
            pd.DataFrame(
                [
                    {"key": "value"},
                    {"item1": 1},
                    {"item2": 2},
                    {"item1": 1},
                    {"item2": 2},
                    {"name": "John", "age": 30},
                    {"name": "Alice", "age": 25},
                ]
            ),
        ),
        (
            "tsv",
            pd.DataFrame({"name": ["Alice", "Bob"], "age": [30, 25]}),
        ),
        # ("tests/tools/test_data/io_helper/", "csv", pd.DataFrame({"name": ["Alice", "Bob"], "age": [30, 25]})),
        (
            "csv",
            pd.DataFrame(
                {
                    "name": ["Alice", "Bob"],
                    "age": pd.Series([30, 25], dtype="float64"),
                    "strtest": pd.Series([np.nan, np.nan], dtype="object"),
                }
            ),
        ),
        ("txt", None),
        # ("tests/tools/test_data/io_helper/dict.json", "json", pd.DataFrame([{"key": "value"}])),
        # ("tests/tools/test_data/io_helper/dict.json", "txt", None),
    ],
)
def test_read_all_files_of_type(filetype, expect_out):
    """
    Test the read_all_files_of_type in tools/io_helpers.py
    :param filetype: file types
    :param expect_out: expect output
    """
    logger = MagicMock()
    current_path = pathlib.Path(__file__).parent.absolute()
    inpath = current_path.joinpath("test_data", "io_helper").resolve()
    out_results = read_all_files_of_type(inpath, filetype, logger)
    if out_results is None:
        assert out_results == expect_out, "out_results and expect_out should be None"
    else:
        try:
            assert_dataframe_equal(out_results, expect_out)
        except AssertionError as e:
            raise AssertionError(f"DataFrames comparison failed: {e}")


@pytest.mark.parametrize(
    "filename, expect_out",
    [
        (
            "list.jsonl",
            pd.DataFrame([{"item1": 1}, {"item2": 2}]),
        ),
        # ("tests/tools/test_data/io_helper", "dict.json", pd.DataFrame([{"key": "value"}])),
        (
            "dataframe.tsv",
            pd.DataFrame({"name": ["Alice", "Bob"], "age": [30, 25]}),
        ),
        (
            "dataframe.csv",
            pd.DataFrame({"name": ["Alice", "Bob"], "age": [30, 25]}),
        ),
        (
            "list.json",
            [{"item1": 1}, {"item2": 2}],
        ),
        ("data.txt", None),
        ("nested/list.json", [{"item1": 1}, {"item2": 2}]),
    ],
)
def test_read_file_in_path(filename, expect_out):
    """
    Test the read_file_in_path in tools/io_helpers.py
    :param filename: file name
    :param expect_out: expect output
    """
    logger = MagicMock()
    current_path = pathlib.Path(__file__).parent.absolute()
    inpath = current_path.joinpath("test_data", "io_helper").resolve()
    out_results = read_file_in_path(inpath, filename, logger)
    if out_results is None:
        assert out_results == expect_out, "expect out is None"
    else:
        try:
            if isinstance(out_results, pd.DataFrame):
                assert_dataframe_equal(out_results, expect_out)
            else:
                assert out_results == expect_out
        except AssertionError as e:
            raise AssertionError(f"DataFrames comparison failed: {e}")


@pytest.mark.parametrize(
    "expect_out",
    [
        {"name": ["Alice", "Bob"], "age": [30, 25]},
    ],
)
def test_load_tsv_dask(expect_out):
    """
    Test the load_tsv_dask in tools/io_helpers.py
    :param expect_out: expect output
    """
    logger = MagicMock()
    current_path = pathlib.Path(__file__).parent.absolute()
    dataset_path = current_path.joinpath("test_data", "io_helper").resolve()
    out_results = load_tsv_dask(dataset_path, logger)
    expect_out = dd.from_dict(expect_out, npartitions=1)

    expect_out = expect_out.compute()
    out_results = out_results.compute()
    try:
        assert_dataframe_equal(out_results, expect_out)
    except AssertionError as e:
        raise AssertionError(f"DataFrames comparison failed: {e}")


@pytest.mark.parametrize(
    "mock_open_side_effect, expected_content, error_type, error_msg_part",
    [
        (None, "mock file content", None, "Reading text file"),
        (FileNotFoundError, None, FileNotFoundError, "File not found"),
        (PermissionError, None, PermissionError, "Permission denied for file"),
        (IOError("IO error"), None, IOError, "IO error occurred while reading file"),
        (
            Exception("Unexpected error"),
            None,
            Exception,
            "An unexpected error 'Unexpected error' occurred while reading file",
        ),
    ],
)
def test_load_text(
    mock_logger, mock_open_side_effect, expected_content, error_type, error_msg_part
):
    """
    Test the load_text in tools/io_helpers.py
    :param mock_logger: mock logger
    :param mock_open_side_effect: mock open side effect
    :param expected_content: expected content
    :param error_type: error type
    :param error_msg_part: error message part
    """
    base_path = os.path.join("/", "mock", "path")  # Use os.path.join for compatibility
    file_name = "test.txt"
    full_path = os.path.join(base_path, file_name)

    with patch("builtins.open", mock_open(read_data="mock file content")) as mock_file:
        mock_file.side_effect = mock_open_side_effect

        if error_type:
            # Expect an error to be logged
            content = load_text(base_path, file_name, mock_logger)
            assert content == expected_content
            mock_logger.error.assert_called_once_with(f"{error_msg_part}: {full_path}")

        else:
            # Expect successful read and info log
            content = load_text(base_path, file_name, mock_logger)
            assert content == expected_content
            mock_logger.info.assert_called_once_with(
                f"Reading text file: {file_name} in path: {base_path}"
            )


@pytest.mark.parametrize(
    "dataset_path, issuffix, suffix, expect_suffix",
    [
        ("dict.json", True, "json", "json"),
        ("dict.json", True, "jsonl", "json"),
        ("dict.json", False, None, "json"),
        ("dict", False, None, None),
        ("dict", False, "json", None),
    ],
)
def test_ensure_suffix(dataset_path, issuffix, suffix, expect_suffix):
    """
    Test the ensure_suffix in tools/io_helpers.py
    :param dataset_path: input dir
    :param issuffix: is input suffix
    :param suffix: suffix input
    :param expect_suffix: expect suffix
    """
    # logger = logging.getLogger()
    logger = MagicMock()
    current_path = pathlib.Path(__file__).parent.absolute()
    dataset_path = str(
        current_path.joinpath("test_data", "io_helper", dataset_path).resolve()
    )
    if issuffix:
        if len(dataset_path.split(".")) == 1:
            expectfname = f"{dataset_path}.{expect_suffix}"
            expectoutsuffix = expect_suffix
        else:
            expectfname = dataset_path
            expectoutsuffix = expect_suffix
        outfname, outsuffix = ensure_suffix(logger, dataset_path, suffix)
        assert outfname == expectfname and outsuffix == expectoutsuffix, (
            f"{outfname} {outsuffix} is different from {expectfname} {expectoutsuffix}"
        )
    else:
        if len(dataset_path.split(".")) == 1:
            expectfname = f"{dataset_path}.{suffix}"
            expectoutsuffix = expect_suffix
            with pytest.raises(ValueError, match="Suffix is required"):
                outfname, outsuffix = ensure_suffix(logger, dataset_path)
            logger.error.assert_any_call(
                f"Filename {dataset_path} does not have a suffix",
            )
        else:
            expectfname = dataset_path
            expectoutsuffix = expect_suffix
            outfname, outsuffix = ensure_suffix(logger, dataset_path)
            assert outfname == expectfname and outsuffix == expectoutsuffix, (
                f"{outfname} {outsuffix} is different from {expectfname} {expectoutsuffix}"
            )


@pytest.mark.parametrize(
    "infile, outfile",
    [
        ("list.json", "list_convert.jsonl"),
        ("dict.json", "dict_convert.jsonl"),
    ],
)
def test_convert_json_array_to_jsonl(tmp_path, infile, outfile):
    """
    Test the convert_json_array_to_jsonl in tools/io_helpers.py
    :param tmp_path: the write path
    :param infile: input file
    :param outfile: output file
    """
    outfile = os.path.join(tmp_path, outfile)
    logger = logging.getLogger()
    current_path = pathlib.Path(__file__).parent.absolute()
    inpath = str(current_path.joinpath("test_data", "io_helper", infile).resolve())
    convert_json_array_to_jsonl(inpath, outfile, logger)
    expect_file_path = str(
        current_path.joinpath("test_data", "io_helper", outfile).resolve()
    )
    assert os.path.exists(outfile), f"Output file {outfile} was not created"
    assert compare_files_ignore_eol(os.path.join(tmp_path, outfile), expect_file_path)


@pytest.mark.parametrize(
    "path, file_exists, utf8_content, iso_content, expected, expected_log_calls",
    [
        # Test case 1: Path is None
        (
            None,
            False,
            None,
            None,
            None,
            [
                ("debug", "Attempting to read file: None"),
                ("info", "No path provided. Returning None."),
            ],
        ),
        # Test case 2: File exists and is UTF-8 encoded
        (
            "utf8_path.txt",
            True,
            "UTF-8 content",
            None,
            "UTF-8 content",
            [
                ("debug", "Attempting to read file: utf8_path.txt"),
                ("info", "Trying to read utf8_path.txt with UTF-8 encoding."),
                ("info", "Successfully read utf8_path.txt with UTF-8 encoding."),
            ],
        ),
        # Test case 3: File exists but UTF-8 decoding fails, falls back to ISO-8859-1
        (
            "iso_path.txt",
            True,
            None,
            "ISO-8859-1 content",
            "ISO-8859-1 content",
            [
                ("debug", "Attempting to read file: iso_path.txt"),
                ("info", "Trying to read iso_path.txt with UTF-8 encoding."),
                (
                    "warning",
                    "UTF-8 decoding failed for iso_path.txt: 'utf-8' codec can't decode byte 0x80 in position 0:"
                    " invalid start byte. Attempting ISO-8859-1 encoding.",
                ),
                ("info", "Successfully read iso_path.txt with ISO-8859-1 encoding."),
            ],
        ),
        # Test case 4: File does not exist
        (
            "nonexistent.txt",
            False,
            None,
            None,
            FileNotFoundError,
            [
                ("debug", "Attempting to read file: nonexistent.txt"),
                ("info", "Trying to read nonexistent.txt with UTF-8 encoding."),
                ("error", "File not found: nonexistent.txt."),
            ],
        ),
        # Test case 5: Both UTF-8 and ISO-8859-1 decoding fail
        (
            "invalid_encoding.txt",
            True,
            None,
            None,
            UnicodeDecodeError,
            [
                ("debug", "Attempting to read file: invalid_encoding.txt"),
                ("info", "Trying to read invalid_encoding.txt with UTF-8 encoding."),
                (
                    "warning",
                    "UTF-8 decoding failed for invalid_encoding.txt: 'utf-8' codec can't decode byte 0x80 in"
                    " position 0: invalid start byte. Attempting ISO-8859-1 encoding.",
                ),
                (
                    "error",
                    "ISO-8859-1 decoding also failed for invalid_encoding.txt: 'iso-8859-1' codec can't decode"
                    " byte 0x80 in position 0: invalid start byte.",
                ),
            ],
        ),
    ],
)
def test_read_file_with_fallback_encoding(
    path,
    file_exists,
    utf8_content,
    iso_content,
    expected,
    expected_log_calls,
):
    """
    Tests the read_file_with_fallback_encoding function with various scenarios,
    including verifying log outputs using a mock logger.

    Args:
        path (str or None): The file path to test.
        file_exists (bool): Whether the file exists.
        utf8_content (str or None): The content to return if UTF-8 decoding is successful.
        iso_content (str or None): The content to return if ISO-8859-1 decoding is successful.
        expected (str or Exception or None): The expected result or exception.
        expected_log_calls (list of tuples): Each tuple contains (logger_method, log_message).
    """
    # Create a mock logger
    mock_logger = MagicMock()

    if not path:
        # When path is None, the function should return None without attempting to open any file
        result = read_file_with_fallback_encoding(mock_logger, path)
        assert result is None
    else:
        if not file_exists:
            # Mock open to raise FileNotFoundError
            mocked = mock_open()
            mocked.side_effect = FileNotFoundError
        else:
            # Mock open behavior based on encoding
            mocked = mock_open()
            if utf8_content is not None:
                # UTF-8 decoding succeeds
                mocked.return_value.read.return_value = utf8_content
            elif iso_content is not None:
                # UTF-8 decoding fails, ISO-8859-1 succeeds
                # First call to open (UTF-8) raises UnicodeDecodeError
                # Second call to open (ISO-8859-1) returns iso_content
                mocked.side_effect = [
                    UnicodeDecodeError("utf-8", b"\x80", 0, 1, "invalid start byte"),
                    mock_open(read_data=iso_content).return_value,
                ]
            else:
                # Both UTF-8 and ISO-8859-1 decoding fail
                mocked.side_effect = [
                    UnicodeDecodeError("utf-8", b"\x80", 0, 1, "invalid start byte"),
                    UnicodeDecodeError(
                        "iso-8859-1", b"\x80", 0, 1, "invalid start byte"
                    ),
                ]

        with patch("builtins.open", mocked):
            if isinstance(expected, type) and issubclass(expected, Exception):
                with pytest.raises(expected):
                    read_file_with_fallback_encoding(mock_logger, path)
            else:
                result = read_file_with_fallback_encoding(mock_logger, path)
                assert result == expected

    # Verify log messages
    for method, message in expected_log_calls:
        # Get the mock method
        mock_method = getattr(mock_logger, method)
        # Check if the method was called with the expected message
        mock_method.assert_any_call(message)


# Example custom class that has to_dict()
class User:
    def __init__(self, user_id, name):
        self.user_id = user_id
        self.name = name

    def to_dict(self):
        return {"user_id": self.user_id, "name": self.name}


class NoToDictClass:
    """A class that does *not* implement to_dict(), to test fallback behavior."""

    def __init__(self, value):
        self.value = value


@pytest.mark.parametrize(
    "input_obj, expected_output",
    [
        # 1) Primitive types
        (42, 42),
        (3.14, 3.14),
        ("hello", "hello"),
        (True, True),
        (None, None),
        # 2) Objects with to_dict()
        (User(user_id=1, name="Alice"), {"user_id": 1, "name": "Alice"}),
        # 3) Lists / tuples
        ([1, 2, 3], [1, 2, 3]),
        ((True, False), [True, False]),  # note that a tuple becomes a list in JSON
        # 4) Dictionaries
        ({"key": "value", "number": 123}, {"key": "value", "number": 123}),
        # 5) Fallback to string
        (NoToDictClass("test"), "<test_fallback_string>"),
    ],
)
def test_make_json_serializable(input_obj, expected_output):
    """
    Test that make_json_serializable behaves correctly for various inputs.
    """
    # Because the fallback is `str(obj)`, we can’t guess the exact string if it’s a custom class
    # without implementing a known __str__. So let's handle that scenario specifically:
    result = make_json_serializable(input_obj)

    # If our expected_output is a placeholder indicating fallback to string, we do a type check:
    if expected_output == "<test_fallback_string>":
        # We expect a string if the object doesn't implement to_dict()
        assert isinstance(result, str)
        # You can also check if the resulting string contains the class name, etc.
        assert "NoToDictClass" in result
    else:
        # Otherwise, we directly compare with the expected output
        assert result == expected_output


@pytest.mark.parametrize(
    "file_path, encoding_error, expected_df, raises_exception",
    [
        (
            "valid_file.tsv",
            None,
            pd.DataFrame({"name": ["Alice", "Bob"], "age": [30, 25]}),
            False,
        ),
        (
            "iso_encoded_file.tsv",
            UnicodeDecodeError("utf-8", b"\xc4", 0, 1, "invalid continuation byte"),
            pd.DataFrame({"name": ["Älice", "Bøb"], "age": [30, 25]}),
            False,
        ),
        (
            "nonexistent_file.tsv",
            None,
            None,
            True,
        ),
    ],
)
def test_load_tsv(
    file_path, encoding_error, expected_df, raises_exception, mock_logger
):
    """
    Test the load_tsv function in tools/io_helpers.py
    :param file_path: the path to the tsv file
    :param encoding_error: error to raise when trying to read with utf-8
    :param expected_df: the expected dataframe
    :param raises_exception: whether the function should raise an exception
    :param mock_logger: the mock logger
    """
    if raises_exception:
        with patch("pandas.read_csv", side_effect=FileNotFoundError):
            with pytest.raises(FileNotFoundError):
                load_tsv(file_path, mock_logger)
    else:
        # Setup the mock for pd.read_csv
        mock_read_csv = MagicMock(return_value=expected_df)

        # Setup the side effect for read_csv if we're testing encoding fallback
        if encoding_error:
            mock_read_csv.side_effect = [encoding_error, expected_df]

        with patch("pandas.read_csv", mock_read_csv):
            result_df = load_tsv(file_path, mock_logger)

            # Verify result
            assert result_df is expected_df

            # Check that read_csv was called with the right parameters
            if encoding_error:
                # Should be called first with utf-8 encoding
                mock_read_csv.assert_any_call(
                    file_path, sep="\t", encoding="utf-8", keep_default_na=False
                )
                # Then with iso-8859-1 encoding
                mock_read_csv.assert_any_call(
                    file_path, sep="\t", encoding="iso-8859-1", keep_default_na=False
                )
                # And we should have a warning log
                mock_logger.warning.assert_called_once()
            else:
                # Should be called only once with utf-8 encoding
                mock_read_csv.assert_called_once_with(
                    file_path, sep="\t", encoding="utf-8", keep_default_na=False
                )


@pytest.mark.parametrize(
    "data, chunk_size, expected_chunks",
    [
        # Dictionary test cases
        ({"a": 1, "b": 2, "c": 3, "d": 4}, 2, [{"a": 1, "b": 2}, {"c": 3, "d": 4}]),
        ({"a": 1, "b": 2, "c": 3}, 2, [{"a": 1, "b": 2}, {"c": 3}]),
        ({"a": 1, "b": 2}, 5, [{"a": 1, "b": 2}]),
        ({"a": 1, "b": 2, "c": 3}, 1, [{"a": 1}, {"b": 2}, {"c": 3}]),
        ({}, 2, []),
        # List test cases
        ([1, 2, 3, 4], 2, [[1, 2], [3, 4]]),
        ([1, 2, 3], 2, [[1, 2], [3]]),
        ([1, 2], 5, [[1, 2]]),
        ([1, 2, 3], 1, [[1], [2], [3]]),
        ([], 2, []),
    ],
)
def test_chunk_data(data, chunk_size, expected_chunks):
    """
    Test the chunk_data in tools/io_helpers.py
    :param data: a dictionary or a list data.
    :param chunk_size: the chunks size to divide the output.
    :param expected_chunks: the expected chunks
    """
    assert list(chunk_data(data, chunk_size)) == expected_chunks


@pytest.mark.parametrize(
    "content, chunks_size, suffix, intype, expected_chunks",
    [
        ({"a": 1, "b": 2, "c": 3, "d": 4}, 1, "json", "dict", 4),
        ({"a": 1, "b": 2, "c": 3, "d": 4}, 3, "json", "dict", 2),
        ({"a": 1, "b": 2, "c": 3, "d": 4}, 4, "json", "dict", 1),
    ],
)
def test_write_chunks_files_to_storage(
    tmp_path, content, chunks_size, suffix, intype, expected_chunks
):
    """
    Test the write_file_to_storage in tools/io_helpers.py
    :param tmp_path: the write path
    :param content: the content to write
    :param chunks_size: the chunks size to divide the output
    :param suffix: the suffix
    :param intype: the content type
    :param expected_chunks: the expected number of files (len(content) % chunks_size)
    """
    logger = MagicMock()
    output_name = f"{intype}"
    write_chunks_files_to_storage(
        content, tmp_path, output_name, chunks_size, suffix, logger
    )

    assert os.path.exists(os.path.join(tmp_path, "chunks")), (
        "Chunks directory was not created"
    )

    for index in range(expected_chunks):
        assert os.path.exists(
            os.path.join(tmp_path, "chunks", f"{output_name}_{index}.{suffix}")
        ), f"file {output_name}_{index}.{suffix}.json was not created"


@pytest.mark.parametrize(
    "json_files, expected_output, list_files_return, error_expected",
    [
        # Test case 1: Normal case with multiple JSON files
        (
            {"file1.json": {"key1": "value1"}, "file2.json": {"key2": "value2"}},
            {"key1": "value1", "key2": "value2"},
            ["file1.json", "file2.json"],
            False,
        ),
        # Test case 2: No JSON files found
        ({}, {}, [], False),
        # Test case 3: list_file_type_in_path returns None
        ({}, None, None, True),
    ],
)
def test_reading_json_dicts(
    json_files, expected_output, list_files_return, error_expected, mock_logger
):
    """
    Test the reading_json_dicts function in tools/io_helpers.py
    :param json_files: dict of filename to file content
    :param expected_output: expected combined dict output
    :param list_files_return: what list_file_type_in_path should return
    :param error_expected: whether an error log is expected
    :param mock_logger: mock logger
    """
    test_dir = "/mock/dir"

    # Mock list_file_type_in_path to return our list of filenames
    with patch(
        "tools.io_helpers.list_file_type_in_path",
        return_value=list_files_return,
    ) as mock_list_files:
        # Mock reading_json to return content based on filename
        with patch(
            "tools.io_helpers.reading_json",
            side_effect=lambda filename, logger: json_files.get(
                os.path.basename(filename), {}
            ),
        ) as mock_read_json:
            result = reading_json_dicts(test_dir, mock_logger)

            # Verify list_file_type_in_path was called correctly
            mock_list_files.assert_called_once_with(
                test_dir, "json", logger=mock_logger
            )

            # Check the result
            assert result == expected_output

            # Verify proper error handling
            if error_expected:
                mock_logger.error.assert_called_once_with(
                    f"Failed to list files in {test_dir}"
                )

            # Verify reading_json was called the correct number of times
            if list_files_return:
                assert mock_read_json.call_count == len(list_files_return)
                for filename in list_files_return:
                    mock_read_json.assert_any_call(filename, mock_logger)


@pytest.mark.parametrize(
    "filename, file_content, expected_output, raises_exception, exception_type",
    [
        # Test case 1: Valid JSON file with dictionary content
        (
            "valid_dict.json",
            '{"key": "value", "number": 123}',
            {"key": "value", "number": 123},
            False,
            None,
        ),
        # Test case 2: Valid JSON file with array content
        (
            "valid_array.json",
            '[{"id": 1, "name": "Alice"}, {"id": 2, "name": "Bob"}]',
            [{"id": 1, "name": "Alice"}, {"id": 2, "name": "Bob"}],
            False,
            None,
        ),
        # Test case 3: File not found
        ("nonexistent.json", None, None, True, FileNotFoundError),
        # Test case 4: Invalid JSON content
        ("invalid.json", "{key: value}", None, True, json.JSONDecodeError),
    ],
)
def test_reading_json(
    filename,
    file_content,
    expected_output,
    raises_exception,
    exception_type,
    mock_logger,
):
    """
    Test the reading_json function in tools/io_helpers.py
    :param filename: name of the file to read
    :param file_content: content to mock in the file
    :param expected_output: expected output from reading_json
    :param raises_exception: whether the function should raise an exception
    :param exception_type: type of exception expected
    :param mock_logger: mock logger
    """
    # Setup mock for open function
    mock_open_obj = (
        mock_open(read_data=file_content) if file_content is not None else mock_open()
    )

    # Configure side_effect for the mock if needed
    if raises_exception:
        if exception_type == FileNotFoundError:
            mock_open_obj.side_effect = FileNotFoundError(f"No such file: '{filename}'")
        elif exception_type == json.JSONDecodeError:
            # When open is called, it will return a file-like object
            # but json.load will fail with JSONDecodeError
            with patch(
                "json.load",
                side_effect=json.JSONDecodeError(
                    "Expecting property name", file_content, 1
                ),
            ):
                with patch("builtins.open", mock_open_obj):
                    with pytest.raises(exception_type):
                        reading_json(filename, mock_logger)

                    # Verify logger calls
                    mock_logger.info.assert_called_once_with(
                        f"Reading json file: {filename}"
                    )
            return

    # Run the test
    with patch("builtins.open", mock_open_obj):
        if raises_exception:
            with pytest.raises(exception_type):
                reading_json(filename, mock_logger)
        else:
            result = reading_json(filename, mock_logger)
            assert result == expected_output

    # Verify logger calls
    mock_logger.info.assert_called_once_with(f"Reading json file: {filename}")

    # Verify open was called with the right parameters if no exception was expected
    if not raises_exception:
        mock_open_obj.assert_called_once_with(filename, "r", encoding="utf-8")


def test_read_all_files_of_json_iterator_with_nan_handling(tmp_path, mock_logger):
    """
    Test the read_all_files_of_json_iterator function with NaN value handling
    :param tmp_path: temporary directory for test files
    :param mock_logger: mock logger
    """
    # Create a test JSON file with NaN values
    test_file_content = """{
  "meeting1": {
    "title": "Test Meeting",
    "enrich_chat_triggers": NaN,
    "participants": ["Alice", "Bob"],
    "rating": 4.5
  },
  "meeting2": {
    "title": "Another Meeting", 
    "enrich_chat_triggers": null,
    "participants": ["Charlie"],
    "rating": NaN
  }
}"""

    # Create test file
    test_file = tmp_path / "test_nan.json"
    test_file.write_text(test_file_content)

    # Test the function
    results = list(
        read_all_files_of_json_iterator(
            str(tmp_path), "json", mock_logger, chunk_size=10
        )
    )

    # Should have exactly one DataFrame result
    assert len(results) == 1
    df = results[0]

    # Check that we have 2 meetings
    assert len(df) == 2

    # Check that NaN values were converted to None (which pandas represents as NaN)
    # meeting1 should have enrich_chat_triggers as None (NaN in pandas)
    meeting1_data = df.loc["meeting1"]
    assert pd.isna(meeting1_data["enrich_chat_triggers"])
    assert meeting1_data["title"] == "Test Meeting"
    assert meeting1_data["rating"] == 4.5

    # meeting2 should have both enrich_chat_triggers and rating as None (NaN in pandas)
    meeting2_data = df.loc["meeting2"]
    assert pd.isna(meeting2_data["enrich_chat_triggers"])  # was null in JSON
    assert pd.isna(meeting2_data["rating"])  # was NaN in JSON
    assert meeting2_data["title"] == "Another Meeting"

    # Verify that warning was logged about fallback parsing
    mock_logger.warning.assert_called()
    warning_calls = [call[0][0] for call in mock_logger.warning.call_args_list]
    assert any("Direct ijson parsing failed" in call for call in warning_calls)


def test_read_all_files_of_json_iterator_normal_operation(tmp_path, mock_logger):
    """
    Test the read_all_files_of_json_iterator function with normal JSON (no NaN values)
    :param tmp_path: temporary directory for test files
    :param mock_logger: mock logger
    """
    # Create a test JSON file with valid JSON
    test_file_content = """{
  "meeting1": {
    "title": "Test Meeting",
    "enrich_chat_triggers": null,
    "participants": ["Alice", "Bob"],
    "rating": 4.5
  },
  "meeting2": {
    "title": "Another Meeting", 
    "enrich_chat_triggers": "some_value",
    "participants": ["Charlie"],
    "rating": 3.8
  }
}"""

    # Create test file
    test_file = tmp_path / "test_normal.json"
    test_file.write_text(test_file_content)

    # Test the function
    results = list(
        read_all_files_of_json_iterator(
            str(tmp_path), "json", mock_logger, chunk_size=10
        )
    )

    # Should have exactly one DataFrame result
    assert len(results) == 1
    df = results[0]

    # Check that we have 2 meetings
    assert len(df) == 2

    # Check data integrity
    meeting1_data = df.loc["meeting1"]
    assert pd.isna(meeting1_data["enrich_chat_triggers"])  # null in JSON
    assert meeting1_data["title"] == "Test Meeting"
    assert meeting1_data["rating"] == 4.5

    meeting2_data = df.loc["meeting2"]
    assert meeting2_data["enrich_chat_triggers"] == "some_value"
    assert meeting2_data["title"] == "Another Meeting"
    assert meeting2_data["rating"] == 3.8

    # Verify that no warning was logged (normal ijson parsing should work)
    mock_logger.warning.assert_not_called()
