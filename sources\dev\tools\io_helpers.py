"""
Helper functions for reading and writing files
"""

import glob
import json
import ijson
import logging
import os
import re
from pathlib import Path
from typing import Union, List, Dict, Optional, Generator
from decimal import Decimal
import numpy as np
import dask.dataframe as dd
import pandas as pd
import shutil
import subprocess
import tempfile
from io import StringIO


def list_file_type_in_path(
    path: str,
    file_type: str,
    logger: logging.Logger = None,
    return_full_path: bool = True,
) -> List[str]:
    """
    List all files of the given type in a directory
    :param path: the path to the directory containing the csv files
    :param file_type: the type of the files to list
    :param logger: the logger
    :param return_full_path: whether to return the full path or just the filename
    :return: the list of files
    """
    logger.info(f"Listing files type {file_type} in: {path}")
    if os.path.isdir(path):
        logger.info(f"Path {path} is a directory")
        files = glob.glob(os.path.join(path, f"*.{file_type}"))
        if return_full_path:
            return files
        return [os.path.basename(file) for file in files]
    elif os.path.isfile(path):
        logger.info(f"Path {path} is a file")
        if not str(path).endswith(f".{file_type}"):
            logger.info(
                f"Path {path} does not contain {file_type} suffix",
            )
            path = f"{path}.{file_type}"
        return [path]
    else:
        logger.error(
            f"Path {path} is not a valid directory or file",
        )
        # Return an empty list if the path is not valid to avoid errors
        return []


def read_all_files_of_type(
    path: str, file_type: str, logger: logging.Logger
) -> pd.DataFrame:
    """
    Read all files of the given type in a directory and concatenate them into a single dataframe.
    :param path: the path to the directory containing the csv files
    :param file_type: the type of the files to read
    :param logger: the logger
    :return: the concatenated dataframe
    """
    logger.info(f"Reading files type {file_type} from: {path}")
    # Check if the provided path is a directory
    all_files = list_file_type_in_path(
        path, file_type, logger=logger, return_full_path=True
    )
    if all_files is None:
        logger.error(f"Failed to list files in {path}")
        return None
    dataframes_list = []
    for filename in all_files:
        logger.info(f"Reading file {filename}")
        df = reading_file_to_pd(file_type, logger, filename)
        dataframes_list.append(df)
    if len(dataframes_list) == 0:
        logger.error(f"No files type {file_type} found in {path}")
        return None
    return pd.concat(dataframes_list, axis=0)


def convert_decimals_to_float(obj):
    """Recursively convert Decimal to float, while handling None values"""
    if obj is None:
        return None
    elif isinstance(obj, Decimal):
        return float(obj)
    elif isinstance(obj, dict):
        return {k: convert_decimals_to_float(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_decimals_to_float(item) for item in obj]
    return obj


def read_all_files_of_json_iterator(
    path: str, file_type: str, logger: logging.Logger, chunk_size: int = 1000
):
    """
    Read all files of the given type in a directory and yield DataFrames in chunks.
    """
    all_files = list_file_type_in_path(path, file_type, logger)

    for file_path in all_files:
        logger.info(f"Processing file: {file_path}")

        # Always preprocess the file to fix potential NaN values and create a temporary file
        logger.info(f"Creating streaming-fixed temporary file for {file_path}")

        try:
            with tempfile.NamedTemporaryFile(
                mode="w+", encoding="utf-8", delete=False
            ) as temp_file:
                temp_file_path = temp_file.name

                # Stream through the original file with a reasonable buffer to fix JSON values
                buffer_size = 8 * (1024**3)  # 64MB buffer to avoid OOM
                buffer = ""

                with open(file_path, "r", encoding="utf-8") as original_file:
                    while True:
                        chunk_data = original_file.read(buffer_size)
                        if not chunk_data:
                            break

                        # Add to buffer
                        buffer += chunk_data

                        # Process complete lines to avoid splitting JSON values
                        lines = buffer.split("\n")
                        buffer = lines[-1]  # Keep incomplete line in buffer

                        for line in lines[:-1]:
                            # Fix invalid JSON values in each line
                            fixed_line = line.replace(": NaN,", ": null,")
                            fixed_line = fixed_line.replace(": NaN}", ": null}")
                            fixed_line = fixed_line.replace(": NaN]", ": null]")
                            fixed_line = fixed_line.replace(":NaN,", ":null,")
                            fixed_line = fixed_line.replace(":NaN}", ":null,")
                            fixed_line = fixed_line.replace(":NaN]", ":null]")

                            # Handle cases with whitespace around NaN
                            fixed_line = re.sub(
                                r":\s*NaN\s*([,}])", r": null\1", fixed_line
                            )

                            # Also handle Infinity values
                            fixed_line = fixed_line.replace(": Infinity,", ": null,")
                            fixed_line = fixed_line.replace(": Infinity}", ": null}")
                            fixed_line = fixed_line.replace(": Infinity]", ": null]")
                            fixed_line = fixed_line.replace(":-Infinity,", ": null,")
                            fixed_line = fixed_line.replace(":-Infinity}", ": null}")
                            fixed_line = fixed_line.replace(":-Infinity]", ": null]")

                            temp_file.write(fixed_line + "\n")

                    # Process remaining buffer
                    if buffer:
                        fixed_line = buffer.replace(": NaN,", ": null,")
                        fixed_line = fixed_line.replace(": NaN}", ": null}")
                        fixed_line = fixed_line.replace(": NaN]", ": null]")
                        fixed_line = fixed_line.replace(":NaN,", ":null,")
                        fixed_line = fixed_line.replace(":NaN}", ":null,")
                        fixed_line = fixed_line.replace(":NaN]", ":null]")
                        fixed_line = re.sub(
                            r":\s*NaN\s*([,}])", r": null\1", fixed_line
                        )
                        fixed_line = fixed_line.replace(": Infinity,", ": null,")
                        fixed_line = fixed_line.replace(": Infinity}", ": null}")
                        fixed_line = fixed_line.replace(": Infinity]", ": null]")
                        fixed_line = fixed_line.replace(":-Infinity,", ": null,")
                        fixed_line = fixed_line.replace(":-Infinity}", ": null}")
                        fixed_line = fixed_line.replace(":-Infinity]", ": null]")
                        temp_file.write(fixed_line)

            # Now use ijson to parse the fixed file
            try:
                with open(temp_file_path, "rb") as fixed_file:
                    parser = ijson.kvitems(fixed_file, "")
                    chunk = {}

                    for meeting_id, meeting_data in parser:
                        chunk[meeting_id] = convert_decimals_to_float(meeting_data)
                        if len(chunk) >= chunk_size:
                            yield pd.DataFrame.from_dict(chunk, orient="index")
                            chunk = {}

                    # handle any remaining items in the last chunk
                    if chunk:
                        yield pd.DataFrame.from_dict(chunk, orient="index")

            except ijson.common.IncompleteJSONError as e:
                logger.warning(f"Direct ijson parsing failed for {file_path}: {e}")
                logger.info(f"Falling back to standard JSON parsing for {file_path}")

                # Final fallback: load the fixed file with standard json
                with open(temp_file_path, "r", encoding="utf-8") as fixed_file:
                    data = json.load(fixed_file)
                    if isinstance(data, dict):
                        # Process in chunks
                        chunk = {}
                        for meeting_id, meeting_data in data.items():
                            chunk[meeting_id] = convert_decimals_to_float(meeting_data)
                            if len(chunk) >= chunk_size:
                                yield pd.DataFrame.from_dict(chunk, orient="index")
                                chunk = {}

                        # handle any remaining items
                        if chunk:
                            yield pd.DataFrame.from_dict(chunk, orient="index")
                    else:
                        logger.error(
                            f"Expected dict but got {type(data)} in {file_path}"
                        )

            # Clean up temporary file
            finally:
                try:
                    os.unlink(temp_file_path)
                except OSError:
                    pass  # Ignore errors when cleaning up temp file

        except json.JSONDecodeError as json_err:
            logger.error(
                f"Failed to parse JSON even after streaming fix for {file_path}: {json_err}"
            )
            raise


def reading_json(filename: str, logger: logging.Logger) -> Dict:
    """
    Read a json file
    :param filename: the filename
    :return: the json content as a dictionary
    """
    logger.info(f"Reading json file: {filename}")
    with open(filename, "r", encoding="utf-8") as f:
        df = json.load(f)
    return df


def reading_json_dicts(dir_path: str, logger: logging.Logger) -> dict:
    """
    Read all json files in a directory and return them as a dictionary
    :param dir_path: the path to the directory
    :param logger: the logger
    :return: the list of dictionaries
    """
    logger.info(f"Reading json files from {dir_path}")
    all_json_files = list_file_type_in_path(dir_path, "json", logger=logger)
    if all_json_files is None:
        logger.error(f"Failed to list files in {dir_path}")
        return None
    data = {}
    for filename in all_json_files:
        data.update(reading_json(filename, logger))
    return data


def reading_file_to_pd(
    file_type: str, logger: logging.Logger, filename: str
) -> Union[pd.DataFrame, Dict]:
    """
    Read a file to a pandas dataframe
    :param file_type: the type of the file
    :param logger: the logger
    :param filename: the filename
    """
    if file_type == "tsv":
        try:
            df = pd.read_csv(filename, index_col=None, header=0, sep="\t")
        except Exception as e:
            logger.error(f"Error parsing TSV file {filename}: {e}")
            with open(filename, "r") as f:
                for i, line in enumerate(f, 1):
                    try:
                        pd.read_csv(StringIO(line), sep="\t")
                    except pd.errors.ParserError:
                        logger.error(
                            f"Error parsing line {i} in TSV file {filename}: {line.strip()[:20]}"
                        )
                        break
            raise e
    elif file_type == "jsonl":
        try:
            df = pd.read_json(filename, lines=True)
        except ValueError:
            with open(filename, "r") as f:
                df = json.load(f)
                if isinstance(df, list):
                    df = pd.DataFrame(df)
                else:
                    df = pd.DataFrame.from_dict(df, orient="index")
            logger.warning("Read non-standard jsonl file")
    elif file_type == "json":
        try:
            df = pd.read_json(filename, lines=False)
            df = df.T
        except ValueError:
            with open(filename, "r") as f:
                df = json.load(f)
                if isinstance(df, list):
                    df = pd.DataFrame(df)
                else:
                    df = pd.DataFrame.from_dict(df, orient="index")
                df = df.T
            logger.warning("Read non-standard json file")
    else:
        logger.error(
            f"File type {file_type} is not recognized, trying as csv",
        )
        df = pd.read_csv(filename, index_col=None, header=0)
    return df


def unzip_gz_in_subfolders(base_dir: str, nrows: int = 100) -> str:
    """
    Recursively search 'base_dir' for *.json.gz files, uncompress them into a
    temp folder via 'gunzip', split them into files with nrows and return the temp folder path.
    """
    tmpdir = tempfile.mkdtemp()
    partitions_files_dir = os.path.join(tmpdir, "partitions")
    os.makedirs(partitions_files_dir, exist_ok=True)
    compressed_file_extension = ".json.gz"

    for root, _, files in os.walk(base_dir):
        for file_name in files:
            if file_name.endswith(compressed_file_extension):
                src_path = os.path.join(root, file_name)
                dst_path = os.path.join(tmpdir, file_name)
                file_name_without_extention = file_name[
                    : -1 * len(compressed_file_extension)
                ]
                shutil.copy(src_path, dst_path)
                subprocess.run(["gunzip", dst_path], check=True)

                uncompressed_file_path = os.path.join(
                    tmpdir, f"{file_name_without_extention}.json"
                )
                subprocess.run(
                    [
                        "split",
                        "-l",
                        str(nrows),
                        "-a",
                        "5",
                        "--additional-suffix=.json",
                        uncompressed_file_path,
                        f"{partitions_files_dir}/{file_name_without_extention}_",
                    ],
                    check=True,
                )

    return partitions_files_dir


def read_file_in_path(
    path: str, file_name: str, logger: logging.Logger
) -> Union[pd.DataFrame, Dict, List]:
    """
    Read a file in a path
    :param path: the path to the file
    :param file_name: the name of the file
    :param logger: the logger
    """
    splitted_file_name = [x for x in os.path.split(file_name) if x]
    if len(splitted_file_name) > 1:
        file_name = os.path.split(file_name)[-1]
        path = os.path.join(path, *splitted_file_name[:-1])
    logger.info(f"Reading file: {file_name} in path: {path}")
    file_type = file_name.split(".")[-1]
    all_type_files_in_path = list_file_type_in_path(path, file_type, logger)
    full_path = os.path.join(path, file_name)
    if len(all_type_files_in_path) == 0:
        logger.error(f"No files type {file_type} found in {path}")
        return None
    if full_path not in all_type_files_in_path:
        logger.error(f"File {file_name} not found in {path}")
        return None
    if file_type == "json":
        with open(full_path, "r", encoding="utf-8") as f:
            return json.load(f)
    return reading_file_to_pd(file_type, logger, full_path)


def load_tsv_dask(dataset_path: str, logger: logging.Logger) -> dd.DataFrame:
    """
    load tsv dataset from a directory
    :param dataset_path: the path to the dataset
    :param logger: the logger
    """
    tsv_path = "*.tsv"
    combined_path = os.path.join(dataset_path, tsv_path)
    logger.info(f"Loading tsv dataset from {combined_path}")
    return dd.read_csv(combined_path, sep="\t")


def load_tsv(file_path: str, logger: logging.Logger) -> pd.DataFrame:
    """
    Load a tsv file into a pandas dataframe
    :param file_path: the path to the tsv file
    :param logger: the logger
    :return: the dataframe
    :raises FileNotFoundError: if the file is not found
    """

    logger.info(f"Loading tsv file from {file_path}")
    try:
        # Todo: May need to handle encoding
        return pd.read_csv(file_path, sep="\t", encoding="utf-8", keep_default_na=False)
    except UnicodeDecodeError:
        logger.warning(f"Failed to decode {file_path} with utf-8, trying iso-8859-1")
        return pd.read_csv(
            file_path, sep="\t", encoding="iso-8859-1", keep_default_na=False
        )
    except FileNotFoundError:
        logger.error(f"File not found: {file_path}")
        raise


def load_text(
    path: str, file_name: str, logger: logging.Logger, encoding="utf8", mode="r"
) -> Optional[str]:
    """
    Given a file under a path, load its text file.
    :param path: the path to the file
    :param file_name: the name of the file
    :param logger: the logger
    :param encoding: the encoding of the file
    :param mode: the mode of the file
    :return: content of the file if read successfully, None otherwise
    """
    try:
        logger.info(f"Reading text file: {file_name} in path: {path}")
        full_path = os.path.join(path, file_name)

        with open(full_path, mode=mode, encoding=encoding) as file:
            content = file.read()

        return content

    except FileNotFoundError:
        logger.error(f"File not found: {full_path}")
    except PermissionError:
        logger.error(f"Permission denied for file: {full_path}")
    except IOError:
        logger.error(f"IO error occurred while reading file: {full_path}")
    except Exception as e:
        logger.error(
            f"An unexpected error '{str(e)}' occurred while reading file: {full_path}"
        )

    return None


def convert_int64_to_int(obj):
    if isinstance(obj, dict):
        return {k: convert_int64_to_int(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_int64_to_int(i) for i in obj]
    elif isinstance(obj, np.int64):
        return int(obj)
    else:
        return obj


def ensure_suffix(
    logger: logging.Logger, filename: str, suffix: str = None
) -> tuple[str, str]:
    """
    Ensure the filename has the correct suffix
    :param logger: the logger
    :param filename: the filename
    :param suffix: the suffix
    :return: the filename and suffix
    """
    filename_parts = filename.split(".")
    # if suffix is provided in the filename, use it
    if len(filename_parts) > 1:
        if not suffix or filename_parts[-1] != suffix:
            logger.error(
                f"Filename {filename} does not match the suffix {suffix if suffix else 'None'}",
            )
            suffix = filename_parts[-1]
    # if filename does not have a suffix, add it
    elif len(filename_parts) == 1:
        if suffix is None:
            logger.error(
                f"Filename {filename} does not have a suffix",
            )
            raise ValueError("Suffix is required")
        else:
            filename = f"{filename}.{suffix}"
    return filename, suffix


def write_file_to_storage(
    content: Union[str, dict, list],
    output_path: str,
    filename: str,
    logger: logging.Logger,
    suffix: str = None,
) -> None:
    """
    Write the content to a file in the output path
    :param content: the content to write
    :param output_path: the output path
    :param filename: the filename including suffix
    :param logger: the logger
    :param suffix: the suffix of the file
    """
    Path(output_path).mkdir(parents=True, exist_ok=True)
    filename, suffix = ensure_suffix(logger, filename, suffix)
    combined_path = os.path.join(output_path, filename)
    logger.info(f"Writing to {combined_path}")
    if suffix == "json":
        if isinstance(content, str):
            logger.error(
                "Content must not be a str for JSON format",
            )
        else:
            # Convert content to JSON-serializable form
            content_serializable = make_json_serializable(content)
            with open(combined_path, "w") as f:
                json.dump(content_serializable, f)
    elif suffix == "jsonl":
        if isinstance(content, list):
            with open(combined_path, "w") as f:
                for item in content:
                    item_serializable = make_json_serializable(item)
                    f.write(json.dumps(item_serializable) + "\n")
        else:
            logger.error("Content must be a list for JSONL format")
    elif suffix in ["tsv", "csv"] and isinstance(content, pd.DataFrame):
        content.to_csv(combined_path, index=False, sep="\t" if suffix == "tsv" else ",")
    elif suffix in ["tsv", "csv"]:
        with open(combined_path, "w") as f:
            f.write(content)
    else:
        logger.error(f"Suffix {suffix} is not supported")


def make_json_serializable(obj):
    """
    Recursively convert objects to JSON-serializable types.
    """
    # Handle primitive types
    if isinstance(obj, (int, float, str, bool, type(None))):
        return obj

    # Handle Decimal
    if isinstance(obj, Decimal):
        return float(obj)

    # Handle numpy types
    try:
        if isinstance(obj, (np.integer, np.int64)):
            return int(obj)
        elif isinstance(obj, (np.floating, np.float64)):
            return float(obj)
        elif isinstance(obj, np.bool_):
            return bool(obj)
    except ImportError:
        pass

    # Handle objects with to_dict method
    if hasattr(obj, "to_dict") and callable(obj.to_dict):
        return make_json_serializable(obj.to_dict())

    # Handle lists and tuples
    if isinstance(obj, (list, tuple)):
        return [make_json_serializable(elem) for elem in obj]

    # Handle dictionaries
    if isinstance(obj, dict):
        return {k: make_json_serializable(v) for k, v in obj.items()}

    # Final fallback: convert to string
    return str(obj)


def convert_json_array_to_jsonl(
    input_file: str, output_file: str, logger: logging.Logger
) -> None:
    """
    Convert a JSON file containing an array of JSON objects to JSONL format.

    :param input_file: Path to the input JSON file containing a JSON array.
    :param output_file: Path to the output JSONL file.
    :param logger: the logger
    """
    # Read the JSON array from the input file
    with open(input_file, "r") as f:
        json_array = json.load(f)

    if isinstance(json_array, dict):
        logger.warning("Input JSON is a single object, converting to list")
        json_array = [json_array]
    elif not isinstance(json_array, list):
        logger.error("Input JSON must be an array")
        return

    # Write each item in the JSON array as a separate line in the output JSONL file
    with open(output_file, "w") as f:
        for item in json_array:
            item_serializable = make_json_serializable(item)
            f.write(json.dumps(item_serializable) + "\n")

    logger.info(f"Conversion complete! JSONL saved to {output_file}")


def read_file_with_fallback_encoding(logger: logging.Logger, path: str):
    """
    Reads the content of a file using UTF-8 encoding.
    If UTF-8 decoding fails, it retries with ISO-8859-1 encoding.
    :param logger: The logger instance.
    :param path: The path to the file.

    Returns:
        str or None: The content of the file, or None if path is None.

    Raises:
        FileNotFoundError: If the file does not exist.
        UnicodeDecodeError: If both UTF-8 and ISO-8859-1 decoding fail.
        Exception: Propagates any other exceptions raised during file reading.
    """
    logger.debug(f"Attempting to read file: {path}")

    if not path:
        logger.info("No path provided. Returning None.")
        return None

    try:
        logger.info(f"Trying to read {path} with UTF-8 encoding.")
        with open(path, encoding="utf8") as f:
            content = f.read()
        logger.info(f"Successfully read {path} with UTF-8 encoding.")
        return content
    except UnicodeDecodeError as e:
        logger.warning(
            f"UTF-8 decoding failed for {path}: {e}. Attempting ISO-8859-1 encoding."
        )
        try:
            with open(path, encoding="iso-8859-1") as f:
                content = f.read()
            logger.info(f"Successfully read {path} with ISO-8859-1 encoding.")
            return content
        except UnicodeDecodeError as e_iso:
            logger.error(f"ISO-8859-1 decoding also failed for {path}: {e_iso}.")
            raise
        except FileNotFoundError:
            logger.error(f"File not found: {path}.")
            raise
        except Exception as e_other:
            logger.error(
                f"An unexpected error occurred while reading {path}: {e_other}."
            )
            raise
    except FileNotFoundError:
        logger.error(f"File not found: {path}.")
        raise
    except Exception as e_other:
        logger.error(f"An unexpected error occurred while reading {path}: {e_other}.")
        raise


def safe_value(val):
    """
    Safely handle potential NaN values by converting them to None
    :param val: The value to check
    """
    return None if pd.isna(val) else val


def chunk_data(data: Union[dict, list], chunk_size: int) -> Generator:
    """
    Yield successive chunk_size-sized dictionaries/lists from data.
    :param data: a dictionary or a list data.
    :param chunk_size: the chunks size to divide the output.
    """
    items = list(data.items()) if isinstance(data, dict) else list(data)
    for i in range(0, len(items), chunk_size):
        yield (
            dict(items[i : i + chunk_size])
            if isinstance(data, dict)
            else items[i : i + chunk_size]
        )


def write_chunks_files_to_storage(
    content: Union[str, dict, list],
    output_path: str,
    file_name: str,
    chunks_size: int,
    suffix: str,
    logger: logging.Logger,
) -> None:
    """
    Write the content to a file in the output path in chunks
    :param content: the content to write
    :param output_path: the output path
    :param file_name: the file name
    :param chunks_size: the chunks size to divide the output
    :param suffix: the suffix of the file
    :param logger: the logger
    """
    chunks_output_path = Path(output_path).joinpath("chunks").resolve()
    for index, chunk in enumerate(chunk_data(content, chunks_size)):
        write_file_to_storage(
            chunk, str(chunks_output_path), f"{file_name}_{index}", logger, suffix
        )


def write_file_to_storage_with_streaming(
    output_path: str,
    filename: str,
    logger: logging.Logger,
    data_iterator: Generator[Dict, None, None],
):
    """
    Write data to a file in the output path using streaming to avoid OOM issues.
    :param output_path: the output path
    :param filename: the filename including suffix
    :param logger: the logger
    :param data_iterator: generator yielding dicts to write
    """
    Path(output_path).mkdir(parents=True, exist_ok=True)
    filename, suffix = ensure_suffix(logger, filename)
    combined_path = os.path.join(output_path, filename)

    if suffix == "json":
        logger.info(f"Writing streaming JSON to {combined_path}")

        # Use true streaming approach - write opening brace and process chunks incrementally
        with open(combined_path, "w", encoding="utf-8") as f:
            f.write("{\n")
            first_chunk = True

            for data_chunk in data_iterator:
                if not data_chunk:
                    continue

                # Make data JSON serializable
                chunk_serializable = make_json_serializable(data_chunk)

                # Write each key-value pair directly to file
                for key, value in chunk_serializable.items():
                    if not first_chunk:
                        f.write(",\n")
                    else:
                        first_chunk = False

                    # Write key-value pair
                    f.write(f"  {json.dumps(str(key))}: {json.dumps(value)}")

            f.write("\n}")

        logger.info(f"Successfully wrote streaming JSON to {combined_path}")

    elif suffix == "jsonl":
        logger.info(f"Writing streaming JSONL to {combined_path}")

        # Write JSONL incrementally
        with open(combined_path, "w", encoding="utf-8") as f:
            for data_item in data_iterator:
                if not data_item:
                    continue

                # Make data JSON serializable
                item_serializable = make_json_serializable(data_item)
                f.write(json.dumps(item_serializable) + "\n")

        logger.info(f"Successfully wrote streaming JSONL to {combined_path}")

    else:
        logger.error(f"Suffix {suffix} is not supported for streaming write")
